group 'vn.hunghd.flutter.plugins.imagecropper'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        jcenter()
        maven { url "https://www.jitpack.io" }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url "https://www.jitpack.io" }
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 28

    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    dependencies {
        implementation 'com.github.yalantis:ucrop:2.2.4'
    }
}
