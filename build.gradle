group 'io.flutter.plugins.urllauncher'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.4.2'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    testOptions {
        unitTests.includeAndroidResources = true
    }
}

dependencies {
    compileOnly 'androidx.annotation:annotation:1.0.0'
    testImplementation 'junit:junit:4.12'
    testImplementation 'org.mockito:mockito-core:1.10.19'
    testImplementation 'androidx.test:core:1.0.0'
    testImplementation 'org.robolectric:robolectric:4.3'
}
