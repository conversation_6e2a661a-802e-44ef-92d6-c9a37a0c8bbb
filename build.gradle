group 'com.umeng.umeng_apm_sdk'
version '1.0'

buildscript {
    repositories {
        google()
        jcenter()
        maven { url 'https://repo1.maven.org/maven2/'}
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.4'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://repo1.maven.org/maven2/'}
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 16
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    api 'com.umeng.umsdk:apm:1.9.3'
}
