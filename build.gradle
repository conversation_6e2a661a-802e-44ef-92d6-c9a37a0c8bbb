group 'vn.hunghd.flutterdownloader'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.3'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 28

    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    implementation 'androidx.work:work-runtime:2.5.0'
    implementation 'androidx.annotation:annotation:1.2.0'
    implementation 'androidx.core:core:1.5.0'
    implementation 'androidx.fragment:fragment:1.3.4'
}
