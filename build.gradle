group 'io.flutter.plugins.videoplayer'
version '1.0-SNAPSHOT'
def args = ["-Xlint:deprecation","-Xlint:unchecked","-Werror"]

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

project.getTasks().withType(JavaCompile){
    options.compilerArgs.addAll(args)
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
        disable 'GradleDependency'
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    dependencies {
        implementation 'com.google.android.exoplayer:exoplayer-core:2.14.1'
        implementation 'com.google.android.exoplayer:exoplayer-hls:2.14.1'
        implementation 'com.google.android.exoplayer:exoplayer-dash:2.14.1'
        implementation 'com.google.android.exoplayer:exoplayer-smoothstreaming:2.14.1'
        testImplementation 'junit:junit:4.12'
        testImplementation 'org.mockito:mockito-inline:3.9.0'
    }


    testOptions {
        unitTests.includeAndroidResources = true
        unitTests.returnDefaultValues = true
        unitTests.all {
            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
