group 'com.xuexiang.flutter_xupdate'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.3'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url "https://jitpack.io" }
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 28

    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
        abortOnError false
    }
}

dependencies {
    implementation 'com.github.xuexiangjys:XUpdate:2.0.6'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.code.gson:gson:2.8.5'
    implementation 'com.zhy:okhttputils:2.6.2'
    implementation 'com.squareup.okhttp3:okhttp:3.14.9'

}
