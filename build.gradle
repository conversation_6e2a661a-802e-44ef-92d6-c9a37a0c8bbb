group 'com.vitanov.multiimagepicker'
version '4.8.00'

buildscript {
    repositories {
        google()
        jcenter()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.4.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    implementation 'androidx.exifinterface:exifinterface:1.3.1'
    implementation 'com.sangcomz:FishBun:0.11.2'
    implementation 'com.github.bumptech.glide:glide:4.11.0'
}
