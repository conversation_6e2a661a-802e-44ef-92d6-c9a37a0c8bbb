group 'me.yohom.amap_core_fluttify'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        jcenter()
        maven { url 'http://download.flutter.io' }
        maven { url "https://oss.sonatype.org/content/groups/public" }
        maven { url "http://maven.aliyun.com/nexus/content/groups/public/" }
        maven { url "https://dl.bintray.com/aweme-open-sdk-team/public" }
        maven { url 'http://developer.huawei.com/repo/' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'http://download.flutter.io' }
        maven { url "https://oss.sonatype.org/content/groups/public" }
        maven { url "http://maven.aliyun.com/nexus/content/groups/public/" }
        maven { url "https://dl.bintray.com/aweme-open-sdk-team/public" }
        maven { url 'http://developer.huawei.com/repo/' }
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 28

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
        main.jniLibs.srcDir 'libs'
    }
    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }
    packagingOptions {
        merge 'res/values/values.xml'
        merge 'AndroidManifest.xml'
        merge 'R.txt'
        merge 'classes.jar'
        merge 'proguard.txt'
    }
    buildTypes {
        release {
            consumerProguardFiles "proguard-rules.pro"
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])
    implementation 'androidx.annotation:annotation:1.1.0'
    implementation 'androidx.appcompat:appcompat:1.1.0'
    compileOnly rootProject.findProject(":foundation_fluttify")
    // flutter plugin dependency
    
    // sdk dependency
    
}
