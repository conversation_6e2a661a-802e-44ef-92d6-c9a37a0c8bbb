group 'com.baseflow.permissionhandler'
version '1.0-SNAPSHOT'

buildscript {
    repositories {
        google()
        jcenter()
        maven { url 'https://maven.google.com' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 16
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
    compileOptions {
        sourceCompatibility = '1.8'
        targetCompatibility = '1.8'
    }
}

dependencies {
    implementation 'androidx.annotation:annotation:1.1.0'
    implementation 'androidx.core:core:1.1.0'
}

repositories {
    google()
}