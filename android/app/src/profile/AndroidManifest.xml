<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.jiangsu.qingtongzhihe.android">
    <!-- Flutter needs it to communicate with the running application
         to allow setting breakpoints, to provide hot reload, etc.
    -->
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.WAKE_LOCK" />
</manifest>
