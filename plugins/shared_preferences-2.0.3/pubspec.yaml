name: shared_preferences
description: Flutter plugin for reading and writing simple key-value pairs.
  Wraps NSUserDefaults on iOS and SharedPreferences on Android.
homepage: https://github.com/flutter/plugins/tree/master/packages/shared_preferences/shared_preferences
version: 2.0.3

flutter:
  plugin:
    platforms:
      android:
        package: io.flutter.plugins.sharedpreferences
        pluginClass: SharedPreferencesPlugin
      ios:
        pluginClass: FLTSharedPreferencesPlugin
      linux:
        default_package: shared_preferences_linux
      macos:
        default_package: shared_preferences_macos
      web:
        default_package: shared_preferences_web

dependencies:
  meta: ^1.3.0
  flutter:
    sdk: flutter
  shared_preferences_platform_interface: ^2.0.0
  # The design on https://flutter.dev/go/federated-plugins was to leave
  # this constraint as "any". We cannot do it right now as it fails pub publish
  # validation, so we set a ^ constraint.
  # TODO(franciscojma): Revisit this (either update this part in the  design or the pub tool).
  # https://github.com/flutter/flutter/issues/46264
  shared_preferences_linux: ^2.0.0
  shared_preferences_macos: ^2.0.0
  shared_preferences_web: ^2.0.0
  shared_preferences_windows: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_driver:
    sdk: flutter
  integration_test:
    path: ../../integration_test
  pedantic: ^1.10.0

environment:
  sdk: ">=2.12.0-259.9.beta <3.0.0"
  flutter: ">=1.12.13+hotfix.5"
