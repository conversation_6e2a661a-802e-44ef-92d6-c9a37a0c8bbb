name: shared_preferences_example
description: Demonstrates how to use the shared_preferences plugin.

dependencies:
  flutter:
    sdk: flutter
  shared_preferences:
    # When depending on this package from a real application you should use:
    #   shared_preferences: ^x.y.z
    # See https://dart.dev/tools/pub/dependencies#version-constraints
    # The example app is bundled with the plugin so we use a path dependency on
    # the parent directory to use the current plugin's version.
    path: ../

dev_dependencies:
  flutter_driver:
    sdk: flutter
  integration_test:
    path: ../../../integration_test
  pedantic: ^1.10.0

flutter:
  uses-material-design: true

environment:
  sdk: ">=2.12.0-259.9.beta <3.0.0"
  flutter: ">=1.9.1+hotfix.2"
