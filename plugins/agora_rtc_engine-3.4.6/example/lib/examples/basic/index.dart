import 'package:agora_rtc_engine_example/examples/basic/join_channel_audio.dart';
import 'package:agora_rtc_engine_example/examples/basic/join_channel_video.dart';
import 'package:agora_rtc_engine_example/examples/basic/string_uid.dart';

/// Data source for basic examples
final Basic = [
  {'name': 'Basic'},
  {'name': 'JoinChannelAudio', 'widget': JoinChannelAudio()},
  {'name': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'widget': <PERSON>inChannelVideo()},
  {'name': 'StringUid', 'widget': StringUid()}
];
