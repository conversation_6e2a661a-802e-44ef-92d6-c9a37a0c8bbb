name: CI

on:
  push:
    branches:
    - master
  pull_request:
  workflow_dispatch:

jobs:
  check-format:
      name: Check format using flutter format .
      runs-on: ubuntu-latest

      steps:
        - name: Checkout code
          uses: actions/checkout@v2
        - uses: subosito/flutter-action@v1
          with:
            channel: beta
        - name: Check format
          run: flutter format -n . --set-exit-if-changed

  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - uses: subosito/flutter-action@v1
        with:
          channel: beta
      - name: Get dependencies for example
        run: flutter pub get
        working-directory: example
      - name: Lint using flutter analyze
        run: flutter analyze

  # test:
  #   name: Test2
  #   runs-on: ubuntu-latest

  #   steps:
  #     - name: Checkout code
  #       uses: actions/checkout@v2
  #     - uses: subosito/flutter-action@v1
  #       with:
  #         channel: beta
  #     - name: Test
  #       run: flutter test
