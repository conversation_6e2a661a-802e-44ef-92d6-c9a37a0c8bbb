//////////////////////////////////////////////////////////
// GENERATED BY FLUTTIFY. DO NOT EDIT IT.
//////////////////////////////////////////////////////////

package me.yohom.amap_location_fluttify.sub_handler;

import android.os.Bundle;
import android.util.Log;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import androidx.annotation.NonNull;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.BinaryMessenger;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry.Registrar;
import io.flutter.plugin.common.StandardMethodCodec;
import io.flutter.plugin.platform.PlatformViewRegistry;

import me.yohom.amap_location_fluttify.AmapLocationFluttifyPlugin.Handler;
import me.yohom.foundation_fluttify.core.FluttifyMessageCodec;

import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getEnableLog;
import static me.yohom.foundation_fluttify.FoundationFluttifyPluginKt.getHEAP;

@SuppressWarnings("ALL")
public class SubHandler2 {
    public static Map<String, Handler> getSubHandler(BinaryMessenger messenger) {
        return new HashMap<String, Handler>() {{
            // method
            put("com.amap.api.location.AMapLocationQualityReport::setGPSSatellites_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setGPSSatellites(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::isWifiAble_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isWifiAble();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::getGPSStatus_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getGPSStatus();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::getGPSSatellites_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getGPSSatellites();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::getNetworkType_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getNetworkType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::setNetworkType_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setNetworkType(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::getNetUseTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Long> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Long __result__ = null;
                    try {
                        __result__ = __this__.getNetUseTime();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::setNetUseTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setNetUseTime(var1.longValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::setInstallHighDangerMockApp_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setInstallHighDangerMockApp(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::isInstalledHighDangerMockApp_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isInstalledHighDangerMockApp();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::setLocationMode_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // enum arg
                    com.amap.api.location.AMapLocationClientOption.AMapLocationMode var1 = com.amap.api.location.AMapLocationClientOption.AMapLocationMode.values()[(int) ((Map<String, Object>) __args__).get("var1")];
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLocationMode(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.location.AMapLocationQualityReport::getAdviseMessage_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.location.AMapLocationQualityReport __this__ = (com.amap.api.location.AMapLocationQualityReport) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getAdviseMessage();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.DistrictItem::getCitycode_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.DistrictItem __this__ = (com.amap.api.fence.DistrictItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getCitycode();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.DistrictItem::setCitycode_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.DistrictItem __this__ = (com.amap.api.fence.DistrictItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCitycode(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.DistrictItem::getAdcode_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.DistrictItem __this__ = (com.amap.api.fence.DistrictItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getAdcode();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.DistrictItem::setAdcode_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.DistrictItem __this__ = (com.amap.api.fence.DistrictItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setAdcode(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.DistrictItem::getPolyline_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.location.DPoint>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.DistrictItem __this__ = (com.amap.api.fence.DistrictItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.location.DPoint> __result__ = null;
                    try {
                        __result__ = __this__.getPolyline();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.DistrictItem::setPolyline_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.location.DPoint> var1 = (java.util.List<com.amap.api.location.DPoint>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.DistrictItem __this__ = (com.amap.api.fence.DistrictItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPolyline(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.DistrictItem::getDistrictName_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.DistrictItem __this__ = (com.amap.api.fence.DistrictItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getDistrictName();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.DistrictItem::setDistrictName_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.DistrictItem __this__ = (com.amap.api.fence.DistrictItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setDistrictName(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::createPendingIntent_batch", (__argsBatch__, __methodResult__) -> {
                List<android.app.PendingIntent> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    android.app.PendingIntent __result__ = null;
                    try {
                        __result__ = __this__.createPendingIntent(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::setActivateAction_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setActivateAction(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::addGeoFence__com_amap_api_location_DPoint__double__String_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.location.DPoint var1 = (com.amap.api.location.DPoint) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var2 = (Number) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    String var3 = (String) ((Map<String, Object>) __args__).get("var3");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.addGeoFence(var1, var2.floatValue(), var3);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::addGeoFence__List_com_amap_api_location_DPoint___String_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.location.DPoint> var1 = (java.util.List<com.amap.api.location.DPoint>) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    String var2 = (String) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.addGeoFence(var1, var2);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::addGeoFence__String__String__com_amap_api_location_DPoint__double__int__String_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    String var2 = (String) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    com.amap.api.location.DPoint var3 = (com.amap.api.location.DPoint) ((Map<String, Object>) __args__).get("var3");
                    // ref arg
                    Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                    // ref arg
                    Number var5 = (Number) ((Map<String, Object>) __args__).get("var5");
                    // ref arg
                    String var6 = (String) ((Map<String, Object>) __args__).get("var6");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.addGeoFence(var1, var2, var3, var4.floatValue(), var5.intValue(), var6);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::addGeoFence__String__String__String__int__String_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    String var2 = (String) ((Map<String, Object>) __args__).get("var2");
                    // ref arg
                    String var3 = (String) ((Map<String, Object>) __args__).get("var3");
                    // ref arg
                    Number var4 = (Number) ((Map<String, Object>) __args__).get("var4");
                    // ref arg
                    String var5 = (String) ((Map<String, Object>) __args__).get("var5");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.addGeoFence(var1, var2, var3, var4.intValue(), var5);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::addGeoFence__String__String_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    String var2 = (String) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.addGeoFence(var1, var2);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::removeGeoFence_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.removeGeoFence();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::removeGeoFence__com_amap_api_fence_GeoFence_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.fence.GeoFence var1 = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.removeGeoFence(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::getAllGeoFence_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.fence.GeoFence>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.fence.GeoFence> __result__ = null;
                    try {
                        __result__ = __this__.getAllGeoFence();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::setGeoFenceAble_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    boolean var2 = (boolean) ((Map<String, Object>) __args__).get("var2");
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setGeoFenceAble(var1, var2);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::pauseGeoFence_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.pauseGeoFence();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::resumeGeoFence_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.resumeGeoFence();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFenceClient::isPause_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFenceClient __this__ = (com.amap.api.fence.GeoFenceClient) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isPause();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getFenceId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getFenceId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setFenceId_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setFenceId(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getCustomId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getCustomId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setCustomId_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCustomId(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getPendingIntentAction_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getPendingIntentAction();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setPendingIntentAction_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPendingIntentAction(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getPendingIntent_batch", (__argsBatch__, __methodResult__) -> {
                List<android.app.PendingIntent> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    android.app.PendingIntent __result__ = null;
                    try {
                        __result__ = __this__.getPendingIntent();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setPendingIntent_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    android.app.PendingIntent var1 = (android.app.PendingIntent) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPendingIntent(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getType_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setType_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setType(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getPoiItem_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.fence.PoiItem> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.fence.PoiItem __result__ = null;
                    try {
                        __result__ = __this__.getPoiItem();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setPoiItem_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.fence.PoiItem var1 = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoiItem(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getDistrictItemList_batch", (__argsBatch__, __methodResult__) -> {
                List<java.util.List<com.amap.api.fence.DistrictItem>> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    java.util.List<com.amap.api.fence.DistrictItem> __result__ = null;
                    try {
                        __result__ = __this__.getDistrictItemList();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setDistrictItemList_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<com.amap.api.fence.DistrictItem> var1 = (java.util.List<com.amap.api.fence.DistrictItem>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setDistrictItemList(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setPointList_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    java.util.List<java.util.List<com.amap.api.location.DPoint>> var1 = (java.util.List<java.util.List<com.amap.api.location.DPoint>>) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPointList(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getRadius_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getRadius();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setRadius_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setRadius(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getExpiration_batch", (__argsBatch__, __methodResult__) -> {
                List<Long> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Long __result__ = null;
                    try {
                        __result__ = __this__.getExpiration();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setExpiration_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setExpiration(var1.longValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getActivatesAction_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getActivatesAction();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setActivatesAction_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setActivatesAction(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getStatus_batch", (__argsBatch__, __methodResult__) -> {
                List<Integer> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Integer __result__ = null;
                    try {
                        __result__ = __this__.getStatus();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setStatus_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setStatus(var1.intValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getEnterTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Long> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Long __result__ = null;
                    try {
                        __result__ = __this__.getEnterTime();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setEnterTime_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setEnterTime(var1.longValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.DPoint> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.location.DPoint __result__ = null;
                    try {
                        __result__ = __this__.getCenter();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setCenter_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.location.DPoint var1 = (com.amap.api.location.DPoint) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCenter(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getMinDis2Center_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getMinDis2Center();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setMinDis2Center_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMinDis2Center(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getMaxDis2Center_batch", (__argsBatch__, __methodResult__) -> {
                List<Float> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Float __result__ = null;
                    try {
                        __result__ = __this__.getMaxDis2Center();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setMaxDis2Center_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setMaxDis2Center(var1.floatValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::isAble_batch", (__argsBatch__, __methodResult__) -> {
                List<Boolean> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Boolean __result__ = null;
                    try {
                        __result__ = __this__.isAble();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setAble_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    boolean var1 = (boolean) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setAble(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::setCurrentLocation_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    com.amap.api.location.AMapLocation var1 = (com.amap.api.location.AMapLocation) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCurrentLocation(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.GeoFence::getCurrentLocation_batch", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.AMapLocation> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.GeoFence __this__ = (com.amap.api.fence.GeoFence) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    com.amap.api.location.AMapLocation __result__ = null;
                    try {
                        __result__ = __this__.getCurrentLocation();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getLatitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Double __result__ = null;
                    try {
                        __result__ = __this__.getLatitude();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setLatitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLatitude(var1.doubleValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getLongitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Double> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Double __result__ = null;
                    try {
                        __result__ = __this__.getLongitude();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setLongitude_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setLongitude(var1.doubleValue());
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getPoiId_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getPoiId();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setPoiId_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoiId(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getPoiType_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getPoiType();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setPoiType_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoiType(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getTypeCode_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getTypeCode();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setTypeCode_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTypeCode(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getAddress_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getAddress();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setAddress_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setAddress(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getTel_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getTel();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setTel_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setTel(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getProvince_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getProvince();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setProvince_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setProvince(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getCity_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getCity();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setCity_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setCity(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getAdname_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getAdname();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::getPoiName_batch", (__argsBatch__, __methodResult__) -> {
                List<String> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
            
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    String __result__ = null;
                    try {
                        __result__ = __this__.getPoiName();
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setPoiName_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setPoiName(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // method
            put("com.amap.api.fence.PoiItem::setAdname_batch", (__argsBatch__, __methodResult__) -> {
                List<Void> __resultList__ = new ArrayList<>();
            
                for (int __i__ = 0; __i__ < ((List<Map<String, Object>>) __argsBatch__).size(); __i__++) {
                    Map<String, Object> __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // ref
                    com.amap.api.fence.PoiItem __this__ = (com.amap.api.fence.PoiItem) ((Map<String, Object>) __args__).get("__this__");
            
                    // invoke native method
                    Void __result__ = null;
                    try {
                        __this__.setAdname(var1);
                    } catch (Throwable throwable) {
                        throwable.printStackTrace();
                        if (getEnableLog()) {
                            Log.d("Current HEAP: ", getHEAP().toString());
                        }
                        __methodResult__.error(throwable.getMessage(), null, null);
                        return;
                    }
            
                    __resultList__.add(__result__);
                }
            
                __methodResult__.success(__resultList__);
            });
            put("RefClass::isKindOfcom_amap_api_location_AMapLocationClient", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.AMapLocationClient);
            });
            put("RefClass::isKindOfcom_amap_api_location_APSService", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.APSService);
            });
            put("RefClass::isKindOfcom_amap_api_location_DPoint", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.DPoint);
            });
            put("RefClass::isKindOfcom_amap_api_location_CoordinateConverter", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.CoordinateConverter);
            });
            put("RefClass::isKindOfcom_amap_api_location_CoordUtil", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.CoordUtil);
            });
            put("RefClass::isKindOfcom_amap_api_location_UmidtokenInfo", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.UmidtokenInfo);
            });
            put("RefClass::isKindOfcom_amap_api_location_AMapLocation", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.AMapLocation);
            });
            put("RefClass::isKindOfcom_amap_api_location_AMapLocationClientOption", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.AMapLocationClientOption);
            });
            put("RefClass::isKindOfcom_amap_api_location_AMapLocationQualityReport", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.location.AMapLocationQualityReport);
            });
            put("RefClass::isKindOfcom_amap_api_fence_DistrictItem", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.fence.DistrictItem);
            });
            put("RefClass::isKindOfcom_amap_api_fence_GeoFenceClient", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.fence.GeoFenceClient);
            });
            put("RefClass::isKindOfcom_amap_api_fence_GeoFence", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.fence.GeoFence);
            });
            put("RefClass::isKindOfcom_amap_api_fence_PoiItem", (__args__, __methodResult__) -> {
                // 引用对象
                Object __this__ = ((Map<String, Object>) __args__).get("__this__");
            
                __methodResult__.success(__this__ instanceof com.amap.api.fence.PoiItem);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_AMapLocationClient__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_AMapLocationClient__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.location.AMapLocationClient __obj__ = new com.amap.api.location.AMapLocationClient(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_AMapLocationClient__android_content_Context__android_content_Intent", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_AMapLocationClient__android_content_Context__android_content_Intent");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                android.content.Intent var2 = (android.content.Intent) ((Map<String, Object>) __args__).get("var2");
            
                // create target object
                com.amap.api.location.AMapLocationClient __obj__ = new com.amap.api.location.AMapLocationClient(var1, var2);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_APSService__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_APSService__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.location.APSService __obj__ = new com.amap.api.location.APSService();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_DPoint__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_DPoint__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.location.DPoint __obj__ = new com.amap.api.location.DPoint();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_DPoint__double__double", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_DPoint__double__double");
                }
            
                // args
                // ref arg
                Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                // ref arg
                Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                // create target object
                com.amap.api.location.DPoint __obj__ = new com.amap.api.location.DPoint(var1.doubleValue(), var3.doubleValue());
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_CoordinateConverter__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_CoordinateConverter__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.location.CoordinateConverter __obj__ = new com.amap.api.location.CoordinateConverter(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_CoordUtil__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_CoordUtil__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.location.CoordUtil __obj__ = new com.amap.api.location.CoordUtil();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_UmidtokenInfo__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_UmidtokenInfo__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.location.UmidtokenInfo __obj__ = new com.amap.api.location.UmidtokenInfo();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_AMapLocation__String", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_AMapLocation__String");
                }
            
                // args
                // ref arg
                String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.location.AMapLocation __obj__ = new com.amap.api.location.AMapLocation(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_AMapLocation__android_location_Location", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_AMapLocation__android_location_Location");
                }
            
                // args
                // ref arg
                android.location.Location var1 = (android.location.Location) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.location.AMapLocation __obj__ = new com.amap.api.location.AMapLocation(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_AMapLocationClientOption__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_AMapLocationClientOption__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.location.AMapLocationClientOption __obj__ = new com.amap.api.location.AMapLocationClientOption();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_location_AMapLocationQualityReport__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_location_AMapLocationQualityReport__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.location.AMapLocationQualityReport __obj__ = new com.amap.api.location.AMapLocationQualityReport();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_fence_DistrictItem__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_fence_DistrictItem__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.fence.DistrictItem __obj__ = new com.amap.api.fence.DistrictItem();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_fence_GeoFenceClient__android_content_Context", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_fence_GeoFenceClient__android_content_Context");
                }
            
                // args
                // ref arg
                android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                // create target object
                com.amap.api.fence.GeoFenceClient __obj__ = new com.amap.api.fence.GeoFenceClient(var1);
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_fence_GeoFence__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_fence_GeoFence__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.fence.GeoFence __obj__ = new com.amap.api.fence.GeoFence();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::createcom_amap_api_fence_PoiItem__", (__args__, __methodResult__) -> {
                if (getEnableLog()) {
                    Log.d("ObjectFactory", "创建对象: com_amap_api_fence_PoiItem__");
                }
            
                // args
            
            
                // create target object
                com.amap.api.fence.PoiItem __obj__ = new com.amap.api.fence.PoiItem();
            
                __methodResult__.success(__obj__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_AMapLocationClient__android_content_Context", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.AMapLocationClient> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
                    // ref arg
                    android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                    // create target object
                    com.amap.api.location.AMapLocationClient __obj__ = new com.amap.api.location.AMapLocationClient(var1);
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_AMapLocationClient__android_content_Context__android_content_Intent", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.AMapLocationClient> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
                    // ref arg
                    android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    android.content.Intent var2 = (android.content.Intent) ((Map<String, Object>) __args__).get("var2");
            
                    // create target object
                    com.amap.api.location.AMapLocationClient __obj__ = new com.amap.api.location.AMapLocationClient(var1, var2);
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_APSService__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.APSService> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.location.APSService __obj__ = new com.amap.api.location.APSService();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_DPoint__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.DPoint> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.location.DPoint __obj__ = new com.amap.api.location.DPoint();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_DPoint__double__double", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.DPoint> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
                    // ref arg
                    Number var1 = (Number) ((Map<String, Object>) __args__).get("var1");
                    // ref arg
                    Number var3 = (Number) ((Map<String, Object>) __args__).get("var3");
            
                    // create target object
                    com.amap.api.location.DPoint __obj__ = new com.amap.api.location.DPoint(var1.doubleValue(), var3.doubleValue());
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_CoordinateConverter__android_content_Context", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.CoordinateConverter> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
                    // ref arg
                    android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                    // create target object
                    com.amap.api.location.CoordinateConverter __obj__ = new com.amap.api.location.CoordinateConverter(var1);
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_CoordUtil__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.CoordUtil> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.location.CoordUtil __obj__ = new com.amap.api.location.CoordUtil();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_UmidtokenInfo__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.UmidtokenInfo> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.location.UmidtokenInfo __obj__ = new com.amap.api.location.UmidtokenInfo();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_AMapLocation__String", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.AMapLocation> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
                    // ref arg
                    String var1 = (String) ((Map<String, Object>) __args__).get("var1");
            
                    // create target object
                    com.amap.api.location.AMapLocation __obj__ = new com.amap.api.location.AMapLocation(var1);
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_AMapLocation__android_location_Location", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.AMapLocation> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
                    // ref arg
                    android.location.Location var1 = (android.location.Location) ((Map<String, Object>) __args__).get("var1");
            
                    // create target object
                    com.amap.api.location.AMapLocation __obj__ = new com.amap.api.location.AMapLocation(var1);
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_AMapLocationClientOption__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.AMapLocationClientOption> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.location.AMapLocationClientOption __obj__ = new com.amap.api.location.AMapLocationClientOption();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_location_AMapLocationQualityReport__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.location.AMapLocationQualityReport> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.location.AMapLocationQualityReport __obj__ = new com.amap.api.location.AMapLocationQualityReport();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_fence_DistrictItem__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.fence.DistrictItem> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.fence.DistrictItem __obj__ = new com.amap.api.fence.DistrictItem();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_fence_GeoFenceClient__android_content_Context", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.fence.GeoFenceClient> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
                    // ref arg
                    android.content.Context var1 = (android.content.Context) ((Map<String, Object>) __args__).get("var1");
            
                    // create target object
                    com.amap.api.fence.GeoFenceClient __obj__ = new com.amap.api.fence.GeoFenceClient(var1);
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_fence_GeoFence__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.fence.GeoFence> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.fence.GeoFence __obj__ = new com.amap.api.fence.GeoFence();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
            // factory
            put("ObjectFactory::create_batchcom_amap_api_fence_PoiItem__", (__argsBatch__, __methodResult__) -> {
                List<com.amap.api.fence.PoiItem> __resultList__ = new ArrayList<>();
            
                int __length__ = 0;
                // when batch size is 0, dart side will put a map with key 'length' to indicate the length
                // of this batch
                if (__argsBatch__ instanceof Map) {
                    __length__ = (Integer) ((Map<String, Object>) __argsBatch__).get("length");
                }
                // or directly put the arg batch
                else if (__argsBatch__ instanceof List) {
                    __length__ = ((List<Map<String, Object>>) __argsBatch__).size();
                }
            
                for (int __i__ = 0; __i__ < __length__; __i__++) {
                    Map<String, Object> __args__ = new HashMap<>();
                    // only when arg batch is not empty, java side needs to parse args;
                    if (__argsBatch__ instanceof List) {
                        __args__ = ((List<Map<String, Object>>) __argsBatch__).get(__i__);
                    }
            
                    // args
            
            
                    // create target object
                    com.amap.api.fence.PoiItem __obj__ = new com.amap.api.fence.PoiItem();
            
                    __resultList__.add(__obj__);
                }
            
                __methodResult__.success(__resultList__);
            });
        }};
    }
}
