import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/account_api.dart';
import 'package:notarization_station_app/service_api/home_api.dart';

import 'common_tools.dart';
import 'event_bus_instance.dart';
import 'global.dart';

class AlertView {
  ///预选房号的底部弹框
  ///@param context 上下文
  ///@param data 数据源
  static showSelectRoomBottomView(
      BuildContext context, List data, Function callback) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return _SelectRoomWidget(data: data, callBack: callback);
        });
  }

  /// 选房超时退出APP

  static showExitApp(BuildContext context) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return Container(
              alignment: Alignment.center,
              margin: EdgeInsets.symmetric(
                  horizontal: 60.0,
                  vertical: (MediaQuery.of(context).size.height - 200) / 2),
              height: 200,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.0),
                  color: Colors.white),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(top: 30.0, bottom: 20.0),
                    child: Text('抱歉，您已超时！',
                        style: TextStyle(
                          fontSize: 19,
                          fontWeight: FontWeight.bold,
                        )),
                  ),
                  Expanded(child: SizedBox()),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      GestureDetector(
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15.0, vertical: 5.0),
                          decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(8.0)),
                          child: Text(
                            '退出APP',
                            style: TextStyle(color: Colors.red),
                          ),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                          exit(0);
                        },
                      ),
                      SizedBox(
                        width: 30.0,
                      ),
                      GestureDetector(
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15.0, vertical: 5.0),
                          decoration: BoxDecoration(
                              color: AppTheme.textBlue,
                              borderRadius: BorderRadius.circular(8.0)),
                          child: Text(
                            '返回首页',
                            style: TextStyle(color: Colors.white),
                          ),
                        ),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.of(context).pushNamedAndRemoveUntil(
                              RoutePaths.HomeIndex, (route) => false);
                        },
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 15.0,
                  ),
                ],
              ));
        });
  }

  static showPhoneAreaAlert(
      BuildContext context, List data, Function onSelectedItemChanged) {
    String selectValue = data[0];
    if (data == null || data.isEmpty) {
      return;
    }
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          constraints: BoxConstraints(maxHeight: 200),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 8.0),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Text('取消'),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        if (selectValue.isEmpty) {
                          EasyLoading.show(status: "请选择区号");
                          return;
                        }
                        Navigator.pop(context);
                        onSelectedItemChanged(selectValue);
                      },
                      child: Text('确定'),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoPicker(
                    itemExtent: 40,
                    onSelectedItemChanged: (index) {
                      selectValue = data[index];
                    },
                    children: data
                        .map((e) => Center(
                              child: Text(
                                '+ $e',
                                style: TextStyle(
                                    fontSize: 15, color: Colors.black),
                              ),
                            ))
                        .toList()),
              ),
            ],
          ),
        );
      },
    );
  }

  static showPickerViewAlert(
      BuildContext context, List data, Function onSelectedItemChanged) {
    String selectValue = data[0];
    if (data == null || data.isEmpty) {
      return;
    }
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          constraints: BoxConstraints(maxHeight: 200),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 8.0),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Text('取消'),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () {
                        if (selectValue.isEmpty) {
                          EasyLoading.show(status: "数据不能为空");
                          return;
                        }
                        Navigator.pop(context);
                        onSelectedItemChanged(selectValue);
                      },
                      child: Text('确定'),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: CupertinoPicker(
                    itemExtent: 40,
                    onSelectedItemChanged: (index) {
                      selectValue = data[index];
                    },
                    children: data
                        .map((e) => Center(
                              child: Text(
                                '$e',
                                style: TextStyle(
                                    fontSize: 15, color: Colors.black),
                              ),
                            ))
                        .toList()),
              ),
            ],
          ),
        );
      },
    );
  }

  // 城市选择器
  static showCityPickerView(BuildContext context,
      {Function resultCallBack, String locationCode}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (ctx) {
        return ConstrainedBox(
          constraints: BoxConstraints(maxHeight: 400),
          child: Container(
            color: Colors.red,
            child: CityPickerView(
              locationCode: locationCode,
              onResult: (res) {
                wjPrint(res.toJson());
                if (resultCallBack != null) {
                  resultCallBack(res);
                }
              },
            ),
          ),
        );
      },
    );
  }
}

///预选房号的底部弹框 widget
// ignore: must_be_immutable
class _SelectRoomWidget extends StatefulWidget {
  List data;
  Function callBack;

  _SelectRoomWidget({Key key, this.data, this.callBack}) : super(key: key);

  @override
  State<_SelectRoomWidget> createState() => __SelectRoomWidgetState();
}

class __SelectRoomWidgetState extends BaseState<_SelectRoomWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: getWidthPx(720),
      decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10.0),
            topRight: Radius.circular(10.0),
          )),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 15.0, top: 20.0, bottom: 10.0),
            child: Text(
              '您可直接选择预选的房号',
              style: TextStyle(
                fontSize: 16.0,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
              child: Container(
            alignment: Alignment.center,
            child: ListView.builder(
              itemBuilder: (context, index) {
                return Container(
                  alignment: Alignment.center,
                  child: GestureDetector(
                    child: Container(
                      margin: const EdgeInsets.symmetric(vertical: 10.0),
                      alignment: Alignment.center,
                      width: 180,
                      height: 40.0,
                      padding: const EdgeInsets.symmetric(
                          vertical: 5.0, horizontal: 10.0),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[200], width: 0.5),
                        borderRadius: BorderRadius.circular(10.0),
                        color: widget.data[index]['isSelected']
                            ? AppTheme.textBlue
                            : Colors.white,
                      ),
                      child: Text(
                        widget.data[index]['name'],
                        style: TextStyle(
                          fontSize: 16.0,
                          color: widget.data[index]["isSelected"]
                              ? Colors.white
                              : AppTheme.textBlue,
                        ),
                      ),
                    ),
                    onTap: () {
                      widget.data.forEach((element) {
                        element['isSelected'] = false;
                        if (element['id'] == widget.data[index]['id']) {
                          element['isSelected'] = true;
                        }
                        setState(() {});
                      });
                    },
                  ),
                );
              },
              itemCount: widget.data.length,
            ),
          )),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20.0),
            child: Center(
              child: Container(
                  padding: const EdgeInsets.symmetric(
                      vertical: 10.0, horizontal: 20.0),
                  decoration: BoxDecoration(
                      border: Border.all(color: AppTheme.textBlue, width: 0.5),
                      borderRadius: BorderRadius.circular(10.0)),
                  child: Text(
                    "放弃预选房号，重新选择",
                    style: TextStyle(
                      fontSize: 16.0,
                      color: AppTheme.textBlue,
                    ),
                  )),
            ),
          ),
          GestureDetector(
            child: Container(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).padding.bottom),
              color: AppTheme.textBlue,
              alignment: Alignment.center,
              height: getWidthPx(100) + MediaQuery.of(context).padding.bottom,
              child: Text(
                '确认',
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 25.0,
                    fontWeight: FontWeight.bold),
              ),
            ),
            onTap: () {
              Navigator.pop(context);
              widget.data.forEach((element) {
                if (element['isSelected']) {
                  widget.callBack(element);
                }
              });
            },
          ),
        ],
      ),
    );
  }
}

// 城市选择
typedef ResultBlock = void Function(CityResult result);

class CityPickerView extends StatefulWidget {
  // 结果返回
  final ResultBlock onResult;
  String locationCode;
  CityPickerView({this.onResult, this.locationCode});
  @override
  _CityPickerViewState createState() => _CityPickerViewState();
}

class _CityPickerViewState extends State<CityPickerView> {
  List<ProvinceModelClass> datas = [];
  int provinceIndex;
  int cityIndex;
  int areaIndex;

  FixedExtentScrollController provinceScrollController;
  FixedExtentScrollController cityScrollController;
  FixedExtentScrollController areaScrollController;

  CityResult result = CityResult();

  bool isShow = false;

  List<ProvinceModelClass> get provinces {
    if (datas.length > 0) {
      if (provinceIndex == null) {
        provinceIndex = 0;
        result.province = provinces[provinceIndex].name;
        result.provinceCode = provinces[provinceIndex].id.toString();
      }
      return datas;
    }
    return [];
  }

  List<CityModelClass> get citys {
    if (provinces.length > 0) {
      return provinces[provinceIndex].treeChild ?? [];
    }
    return [];
  }

  List<AreaModelClass> get areas {
    if (citys.length > 0) {
      if (cityIndex == null) {
        cityIndex = 0;
        result.city = citys[cityIndex].name;
        result.cityCode = citys[cityIndex].id.toString();
      }
      List<AreaModelClass> list = citys[cityIndex].treeChild ?? [];
      if (list.length > 0) {
        if (areaIndex == null) {
          areaIndex = 0;
          result.area = list[areaIndex].name;
          result.areaCode = list[areaIndex].id.toString();
        }
      }
      return list;
    }
    return [];
  }

  // 保存选择结果
  _saveInfoData() {
    var prs = provinces;
    var cts = citys;
    var ars = areas;
    if (provinceIndex != null && prs.length > 0) {
      result.province = prs[provinceIndex].name;
      result.provinceCode = prs[provinceIndex].id.toString();
    } else {
      result.province = '';
      result.provinceCode = '';
    }

    if (cityIndex != null && cts.length > 0) {
      result.city = cts[cityIndex].name;
      result.cityCode = cts[cityIndex].id.toString();
    } else {
      result.city = '';
      result.cityCode = '';
    }

    if (areaIndex != null && ars.length > 0) {
      result.area = ars[areaIndex].name;
      result.areaCode = ars[areaIndex].id.toString();
    } else {
      result.area = '';
      result.areaCode = '';
    }
  }

  @override
  void dispose() {
    provinceScrollController.dispose();
    cityScrollController.dispose();
    areaScrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    //初始化控制器
    provinceScrollController = FixedExtentScrollController();
    cityScrollController = FixedExtentScrollController();
    areaScrollController = FixedExtentScrollController();

    //读取city.json数据
    _loadCitys();
  }

  Future _loadCitys() async {
    final response = await HomeApi.getSingleton().getProvinceAndCiteMessage(2,
        errorCallBack: (msg) {
      ToastUtil.showErrorToast(msg.toString());
    });
    if (response['code'] == 200) {
      if (response['data'] != null && response['data'].isNotEmpty) {
        try {
          datas = response['data']
              .map<ProvinceModelClass>((e) => ProvinceModelClass.fromMap(e))
              .toList();
          if (widget.locationCode != null && widget.locationCode.isNotEmpty) {
            ;
            datas.forEach((element) {
              element.treeChild.forEach((element1) {
                if (element1.id.toString() == widget.locationCode) {
                  cityIndex = element.treeChild.indexOf(element1);
                  provinceIndex = datas.indexOf(element);
                  result.province = element.name;
                  result.provinceCode = element.id.toString();
                  result.city = element1.name;
                  result.cityCode = element1.id.toString();
                  cityScrollController =
                      FixedExtentScrollController(initialItem: cityIndex);
                  provinceScrollController =
                      FixedExtentScrollController(initialItem: provinceIndex);
                  setState(() {});
                }
              });
            });
          }
        } catch (e) {
          wjPrint("报错信息：$e");
        }
      } else {
        datas = [];
      }
      setState(() {
        isShow = true;
      });
    } else {
      ToastUtil.showErrorToast("获取城市信息失败");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            _firstView(),
            _contentView(),
          ],
        ),
      ),
    );
  }

  Widget _firstView() {
    return Container(
      height: 44,
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            FlatButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            FlatButton(
              child: Text('确定'),
              onPressed: () {
                if (widget.onResult != null) {
                  widget.onResult(result);
                }
                Navigator.pop(context);
              },
            ),
          ]),
      decoration: BoxDecoration(
        border: Border(
            bottom: BorderSide(color: Colors.grey.withOpacity(0.1), width: 1)),
      ),
    );
  }

  Widget _contentView() {
    return Container(
      // color: Colors.orange,
      height: 300,
      child: isShow
          ? Row(
              children: <Widget>[
                Expanded(child: _provincePickerView()),
                Expanded(child: _cityPickerView()),
                // Expanded(child: _areaPickerView()),
              ],
            )
          : Center(
              child: CupertinoActivityIndicator(
                animating: true,
              ),
            ),
    );
  }

  Widget _provincePickerView() {
    return Container(
      child: CupertinoPicker(
        scrollController: provinceScrollController,
        children: provinces.map((item) {
          return Center(
            child: Text(
              item.name,
              style: TextStyle(color: Colors.black87, fontSize: 16),
              maxLines: 1,
            ),
          );
        }).toList(),
        onSelectedItemChanged: (index) {
          provinceIndex = index;
          if (cityIndex != null) {
            cityIndex = 0;
            if (cityScrollController.positions.length > 0) {
              cityScrollController.jumpTo(0);
            }
          }
          if (areaIndex != null) {
            areaIndex = 0;
            if (areaScrollController.positions.length > 0) {
              areaScrollController.jumpTo(0);
            }
          }
          _saveInfoData();
          setState(() {});
        },
        itemExtent: 36,
      ),
    );
  }

  Widget _cityPickerView() {
    return Container(
      child: citys.length == 0
          ? Container()
          : CupertinoPicker(
              scrollController: cityScrollController,
              children: citys.map((item) {
                return Center(
                  child: Text(
                    item.name,
                    style: TextStyle(color: Colors.black87, fontSize: 16),
                    maxLines: 1,
                  ),
                );
              }).toList(),
              onSelectedItemChanged: (index) {
                cityIndex = index;
                if (areaIndex != null) {
                  areaIndex = 0;
                  if (areaScrollController.positions.length > 0) {
                    areaScrollController.jumpTo(0);
                  }
                }
                _saveInfoData();
                setState(() {});
              },
              itemExtent: 36,
            ),
    );
  }

  Widget _areaPickerView() {
    return Container(
      width: double.infinity,
      child: areas.length == 0
          ? Container()
          : CupertinoPicker(
              scrollController: areaScrollController,
              children: areas.map((item) {
                return Center(
                  child: Text(
                    item.name,
                    style: TextStyle(color: Colors.black87, fontSize: 16),
                    maxLines: 1,
                  ),
                );
              }).toList(),
              onSelectedItemChanged: (index) {
                areaIndex = index;
                _saveInfoData();
                setState(() {});
              },
              itemExtent: 36,
            ),
    );
  }
}

class ProvinceModelClass {
  // 省、市、区id
  int id;
  // 省、市、区名称
  String name;
  // 省、市、区 拼写
  String alpha;
  // 包含的三级区域
  List<CityModelClass> treeChild;

  ProvinceModelClass.fromMap(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    alpha = json['alpha'];
    if (json['treeChild'] != null && json['treeChild'].isNotEmpty) {
      treeChild = (json['treeChild'] as List)
          .map((e) => CityModelClass.fromMap(e))
          .toList();
    } else {
      treeChild = [];
    }
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['alpha'] = alpha;
    if (treeChild.isEmpty) {
      data['treeChild'] = [];
    } else {
      data['treeChild'] = treeChild.map((e) => e.toMap());
    }
    return data;
  }
}

class CityModelClass {
  // 省、市、区id
  int id;
  // 省、市、区名称
  String name;
  // 省、市、区 拼写
  String alpha;
  // 包含的三级区域
  List<AreaModelClass> treeChild;

  CityModelClass.fromMap(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    alpha = json['alpha'];
    if (json['treeChild'] != null && json['treeChild'].isNotEmpty) {
      treeChild = (json['treeChild'] as List)
          .map((e) => AreaModelClass.fromMap(e))
          .toList();
    } else {
      treeChild = [];
    }
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['alpha'] = alpha;
    if (treeChild.isEmpty) {
      data['treeChild'] = [];
    } else {
      data['treeChild'] = treeChild.map((e) => e.toMap());
    }
    return data;
  }
}

class AreaModelClass {
  // 省、市、区id
  int id;
  // 省、市、区名称
  String name;
  // 省、市、区 拼写
  String alpha;

  AreaModelClass.fromMap(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    alpha = json['alpha'];
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['alpha'] = alpha;
    return data;
  }
}

class CityResult {
  String province;
  String provinceCode;
  String city;
  String cityCode;
  String area;
  String areaCode;
  CityResult(
      {this.province,
      this.provinceCode,
      this.city,
      this.cityCode,
      this.area,
      this.areaCode});

  CityResult.fromJson(Map<String, dynamic> json) {
    province = json['province'];
    city = json['city'];
    area = json['area'];
    provinceCode = json['provinceCode'];
    cityCode = json['cityCode'];
    areaCode = json['areaCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> datas = new Map<String, dynamic>();
    datas['province'] = this.province;
    datas['city'] = this.city;
    datas['area'] = this.area;
    datas['provinceCode'] = this.provinceCode;
    datas['cityCode'] = this.cityCode;
    datas['areaCode'] = this.areaCode;

    return datas;
  }
}

// 弹出对话框
Future<bool> showDownloadApkProgress(BuildContext context) {
  return showDialog<bool>(
    barrierDismissible: false,
    context: context,
    builder: (context) {
      return WillPopScope(
        onWillPop: () async {
          return Future.value(false);
        },
        child: DownloadApkProgress(),
      );
    },
  );
}

class DownloadApkProgress extends StatefulWidget {
  const DownloadApkProgress({Key key}) : super(key: key);

  @override
  State<DownloadApkProgress> createState() => _DownloadApkProgressState();
}

class _DownloadApkProgressState extends State<DownloadApkProgress> {
  int progress = 0;

  StreamSubscription streamSubscription;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    streamSubscription = eventBus.on().listen((event) {
      if (event['name'] == "downloadApkCallback") {
        if (mounted) {
          if (event['status'] == DownloadTaskStatus.failed) {
            ToastUtil.showErrorToast("网络异常请稍后再试");
            return;
          }
          setState(() {
            progress = event['progress'];
          });
        }
        Future.delayed(const Duration(milliseconds: 200), () {
          if (progress == 100) {
            Navigator.pop(context);
            streamSubscription.cancel();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    streamSubscription.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 30),
        padding: EdgeInsets.symmetric(horizontal: 15),
        height: 80,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            color: Colors.white),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text("更新中..."),
            ),
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: progress / 100,
                    backgroundColor: Colors.grey[200],
                    valueColor: AlwaysStoppedAnimation(AppTheme.themeBlue),
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Text("$progress%")
              ],
            ),
          ],
        ),
      ),
    );
  }
}

//图形验证码弹框
showCaptchaCodeAlert(
    {BuildContext context,
    Map<String, dynamic> imageData,
    bool isForgetPassword,
    Function confirmEvent,
    Function cancelEvent,
    Function errorCallBack,
    Function successCallBack,
    Function failCallBack}) {
  showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return WillPopScope(
            child: Material(
                color: Colors.transparent,
                child: CaptchaCodeView(
                    imageData: imageData,
                    isForgetPassword: isForgetPassword,
                    confirmEvent: confirmEvent,
                    cancelEvent: cancelEvent,
                    errorCallBack: errorCallBack,
                    successCallBack: successCallBack,
                    failCallBack: failCallBack)),
            onWillPop: () async {
              return Future.value(false);
            });
      });
}

// 图形验证码弹框界面
class CaptchaCodeView extends StatefulWidget {
  final Map<String, dynamic> imageData;
  final bool isForgetPassword;
  final Function confirmEvent;
  final Function cancelEvent;
  final Function errorCallBack;
  final Function successCallBack;
  final Function failCallBack;
  CaptchaCodeView(
      {Key key,
      this.imageData,
      this.confirmEvent,
      this.cancelEvent,
      this.errorCallBack,
      this.successCallBack,
      this.failCallBack,
      this.isForgetPassword = true})
      : super(key: key);

  @override
  State<CaptchaCodeView> createState() => _CaptchaCodeViewState();
}

class _CaptchaCodeViewState extends State<CaptchaCodeView> {
  TextEditingController _controller = TextEditingController();

  String imageString = '';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.isForgetPassword) {
      forgetPasswordGetCaptchaCode();
    } else {
      registerGetCaptchaCode();
    }
  }

  // 忘记密码部分获取图形验证码
  void forgetPasswordGetCaptchaCode() async {
    AccountApi.getSingleton().sendCaptchaCodeForget(widget.imageData,
        errorCallBack: (e) {
      ToastUtil.showWarningToast('获取图形验证码失败');
      widget.errorCallBack(e);
    }).then((value) {
      if (value != null) {
        widget.successCallBack(value);
        if (value != null && value['code'] == 200) {
          setState(() {
            imageString = value['data'];
          });
        }
      } else {
        widget.failCallBack();
        ToastUtil.showErrorToast(value['message']);
      }
    });
  }

  // 注册部分获取图形验证码
  void registerGetCaptchaCode() async {
    AccountApi.getSingleton().sendCaptchaCode(widget.imageData,
        errorCallBack: (e) {
      ToastUtil.showWarningToast('获取图形验证码失败');
      widget.errorCallBack(e);
      // abroadCodeCountDownIsEnable = true;
      // notifyListeners();
    }).then((value) {
      if (value != null) {
        widget.successCallBack(value);
        if (value['code'] == 200) {
          setState(() {
            imageString = value['data'];
          });
        }
      } else {
        widget.failCallBack();
        ToastUtil.showErrorToast(value['message']);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Spacer(
          flex: 1,
        ),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 50),
          padding: EdgeInsets.only(left: 20, right: 20, bottom: 30),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              color: Colors.white),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                alignment: Alignment.topRight,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text('图形验证码',
                            style: TextStyle(
                                fontSize: 18,
                                color: Colors.black,
                                fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ),
                  GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                        widget.cancelEvent();
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(top: 15),
                        child: Icon(
                          Icons.close,
                          color: Colors.grey[200],
                        ),
                      ))
                ],
              ),
              Divider(
                height: 1,
                color: Colors.grey[200],
              ),
              Stack(
                alignment: Alignment.topRight,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10, vertical: 20),
                    width: ScreenUtil.getInstance().screenWidth - 20,
                    child: imageString.isEmpty
                        ? const SizedBox()
                        : Image.memory(
                            base64Decode(imageString),
                            width: 100,
                            height: 40,
                          ),
                  ),
                  GestureDetector(
                      onTap: () {
                        if (widget.isForgetPassword) {
                          forgetPasswordGetCaptchaCode();
                        } else {
                          registerGetCaptchaCode();
                        }
                      },
                      child: Icon(
                        Icons.refresh,
                        color: Colors.grey[200],
                      ))
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      decoration: InputDecoration(
                        hintText: "请输入图形验证码",
                        fillColor: Colors.grey[200],
                        filled: true,
                        hoverColor: Colors.grey[200],
                        hintStyle: TextStyle(fontSize: 14, color: Colors.grey),
                        border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none),
                        isCollapsed: true,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  GestureDetector(
                    onTap: () {
                      G.pop();
                      widget.confirmEvent(_controller.text);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                      decoration: BoxDecoration(
                          color: AppTheme.themeBlue,
                          borderRadius: BorderRadius.circular(8)),
                      child: Text(
                        '确定',
                        style: TextStyle(fontSize: 15, color: AppTheme.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Spacer(
          flex: 1,
        ),
      ],
    );
  }
}
