/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-28 09:09:57
 * @LastEditors: <PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-03-14 15:57:46
 * @FilePath: /sc-remotenotarization-app/lib/utils/pay_tools.dart
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
import 'package:fluwx/fluwx.dart';
import 'package:notarization_station_app/config.dart';
import 'package:tobias/tobias.dart' ;

import 'common_tools.dart';


class PayTools{
  /// 微信初始化 wx5c765fa201060140
  static initFluwx() async {
    var value = await registerWxApi(
        appId: "wx5c765fa201060140",
        doOnAndroid: true,
        doOnIOS: true,
        universalLink: "https://com.jiangsu.qtzh/"); //
    if (value) {
      if (await isWeChatInstalled) {
        return true;
      } else {
        wjPrint('系统未检测到微信应用');
        return false;
      }
    } else {
      wjPrint('微信初始化失败！');
      return false;
    }
  }
  /// 微信是否安装
  static weChatIsInstall()async{
    return await isWeChatInstalled;
  }
  ///微信支付
  static payWithFluwx(
      {
        String partnerId,
        String prepayId,
        String packageValue,
        String nonceStr,
        int timeStamp,
        String sign,
        Function result}) {
    try {
      payWithWeChat(
          appId: "wx5c765fa201060140",
          partnerId: partnerId,
          prepayId: prepayId,
          packageValue: packageValue,
          nonceStr: nonceStr,
          timeStamp: timeStamp,
          sign: sign)
          .then((value) => result(value));
    } catch (e) {
      wjPrint('微信支付e===$e');
    }
  }

  /// 支付宝是否安装
  static aliPayIsInstall()async{
   return await isAliPayInstalled();
  }

  ///支付宝支付
  static payWithAliPay({String payInfo,Function resultCallBack}){
     aliPay(payInfo).then((value){
       if(resultCallBack!=null){
         resultCallBack.call(value);
       }
     });
  }
}


