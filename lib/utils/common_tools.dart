/*
 * @Author: <EMAIL> WSBwsb123!@#
 * @Date: 2024-04-01 10:39:09
 * @LastEditors: <EMAIL> WSBwsb123!@#
 * @LastEditTime: 2024-07-11 09:27:10
 * @FilePath: /sc-remotenotarization-app/lib/utils/common_tools.dart
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:dart_sm/dart_sm.dart';
import 'package:flutter/foundation.dart';
import 'package:notarization_station_app/utils/string_common.dart';

import 'global.dart';

/// s4 数据加密
String wjEncrypt(dynamic data,String sm4KeyString ) {
  String jsonString = "";
  if (data == null){
    jsonString = jsonEncode("");
  }else{
    jsonString = jsonEncode(data);
  }

  String sm4String = SM4.encrypt(jsonString, key: sm4KeyString);
  // String sm2String = SM2.encrypt(sm4String, G.sm2PublicKey, cipherMode: 1);
  return sm4String;
}

/// 加密sm4公钥
String encryptSM4Key(String sm4String){
  String sm4EncryptString = SM2.encrypt(sm4String, G.sm2PublicKey, cipherMode: 1);
  return "04$sm4EncryptString";
}

/// 获取sm4公钥
 String getPublicKey() {
  return generateRandomHex128();
}

/// sm2解密
dynamic wjDecrypt(dynamic data,) {
  String sm4String = SM2.decrypt(data, G.sm2PrivateKey);
  return jsonDecode(sm4String);
}

/// 获取当前项目的公钥还是私钥
void getProjectEncryptSetting(){
  if (Platform.isIOS){
    G.clientId = G.iosClientId;
    G.sm2PublicKey = G.iosRequestPublic;
    G.sm2PrivateKey = G.iosResponsePrivate;
    G.secret = G.iosSecret;
  }else if(Platform.isAndroid){
    G.clientId = G.androidClientId;
    G.secret = G.androidSecret;
    G.sm2PublicKey = G.androidRequestPublic;
    G.sm2PrivateKey = G.androidResponsePrivate;
  }
  wjPrint("G.clientId: ${G.clientId} \n");
  wjPrint("G.secret: ${G.secret} \n");
  wjPrint("G.sm2PublicKey: ${G.sm2PublicKey} \n");
  wjPrint("G.sm2PrivateKey: ${G.sm2PrivateKey} \n");
}


/// 生成随机 128 比特（16 字节）的十六进制字符串
String generateRandomHex128() {
  final random = Random();
  // 生成16个随机字节
  final bytes = List.generate(16, (index) => random.nextInt(256));
  // 将字节列表转换为十六进制字符串
  return bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join();
}

/// version1:远程版本
/// version2:本地版本
// 代码版本比较
int compareAppVersions(String version1, String version2) {
  List<int> version1Components = version1.split('.').map(int.parse).toList();
  List<int> version2Components = version2.split('.').map(int.parse).toList();

  int maxLength = version1Components.length > version2Components.length
      ? version1Components.length
      : version2Components.length;

  for (int i = 0; i < maxLength; i++) {
    int value1 = (i < version1Components.length) ? version1Components[i] : 0;
    int value2 = (i < version2Components.length) ? version2Components[i] : 0;

    if (value1 < value2) return -1;
    if (value1 > value2) return 1;
  }
  return 0;
}

/// sm4加密
// String sm4Encrypt(String jsonString){
//   String sm4String = SM4.encrypt(jsonString, key: G.sm4PublicKey);
//   return sm4String;
// }


/// 判断输入的字符串是中文还是英文
List<String> judgeChineseOrEnglishName(String name) {
  List<String> tempList = [];
  if (name == null || name.isEmpty) {
    return ['null'];
  }
  if (name.isChinese()) {
    if (name.contains('·')) {
      name.replaceAll('·', '');
      tempList = name.split('');
      return tempList;
    }
    tempList = name.split("");
    return tempList;
  } else if (name.isEnglish()) {
    if (name.endsWith(" ")) {
      name = name.substring(0, name.length - 1);
    }
    if (name.startsWith(' ')) {
      name = name.substring(1, name.length);
    }
    if (name.contains(" ")) {
      tempList = name.split(" ");
      return tempList;
    } else if (name.contains(",")) {
      tempList = name.split(",");
      return tempList;
    } else if (name.contains("/")) {
      tempList = name.split("/");
      return tempList;
    } else if (name.length <= 5) {
      tempList.add(name);
      return tempList;
    } else if (name.length > 5) {
      int len = name.length ~/ 5;
      int segment = name.length % 5;
      segment > 0 ? len++ : len;
      for (int i = 0; i < len; i++) {
        if (i == len - 1) {
          tempList.add(name.substring(i * 5, name.length));
        } else {
          tempList.add(name.substring(i * 5, (i + 1) * 5));
        }
      }
      return tempList;
    }
    return ["null"];
  } else if (name.length <= 5) {
    tempList.add(name);
    return tempList;
  } else if (name.length > 5) {
    int len = name.length ~/ 5;
    int segment = name.length % 5;
    segment > 0 ? len++ : len;
    for (int i = 0; i < len; i++) {
      if (i == len - 1) {
        tempList.add(name.substring(i * 5, name.length));
      } else {
        tempList.add(name.substring(i * 5, (i + 1) * 5));
      }
    }
    return tempList;
  }
}

void wjPrint(Object data) {
  if (!kReleaseMode) {
    print(data);
  }
}
