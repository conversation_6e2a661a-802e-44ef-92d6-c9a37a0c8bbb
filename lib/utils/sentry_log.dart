// /*
//  * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
//  * @Date: 2023-02-13 14:24:58
//  * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
//  * @LastEditTime: 2023-02-13 17:52:02
//  * @FilePath: /remouldApp/lib/utils/sentry_log.dart
//  * @Description: 报错bug收集类
//  *
//  * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved.
//  */
// import 'package:flutter/foundation.dart';
// import 'package:sentry_flutter/sentry_flutter.dart';
//
// class SentryLog {
//   SentryLog._();
//
//   static void init({Function initFunction}) async {
//     String dsn = '';
//     if(kDebugMode){
//       dsn = 'http://40a9a6c4f14b48a1a4afc4e867cfe17b@***********:9000/11';
//     }else{
//       dsn = 'http://8924ea2fd52043b7ac750dd288f5179b@***********:9000/12';
//     }
//     await SentryFlutter.init(
//       (options) {
//         options.dsn = dsn;
//         options.useNativeBreadcrumbTracking();
//
//       },
//       appRunner: initFunction(),
//     );
//   }
//
//   static void catchBugLogs(exception, stackTrace,hint) async {
//     await Sentry.captureException(exception, stackTrace: stackTrace, hint: hint??'');
//   }
//
//     static void catchBugMessage(String message, {
//     SentryLevel level,
//     String template,
//     List<dynamic> params,
//     dynamic hint,
//   }) async {
//     await Sentry.captureMessage(message,level: level,template: template,params: params,hint: hint);
//   }
// }
