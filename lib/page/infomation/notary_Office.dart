import 'package:flutter/material.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/utils/refresh_helper.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/infomation/vm/notary_office_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class NotaryOfficePage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return NotaryOfficePageState();
  }
}

class NotaryOfficePageState extends BaseState<NotaryOfficePage> {
  NotaryOfficeModel notaryModel;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          elevation: 0.0,
          leading: InkWell(
            child: Icon(
              Icons.arrow_back_ios,
              color: Colors.black,
              size: 22,
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          backgroundColor: AppTheme.white,
          centerTitle: true,
          title: Text(
            "公证处",
            style: TextStyle(color: Colors.black, fontSize: 18),
          ),
          actions: [
            InkWell(
              onTap: () {
                G.pushNamed(RoutePaths.Search);
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: Icon(Icons.search, color: Colors.black),
              ),
            )
          ],
        ),
        backgroundColor: AppTheme.chipBackground,
        body: Consumer<UserViewModel>(builder: (ctx, userModel, child) {
          return ProviderWidget<NotaryOfficeModel>(
            model: NotaryOfficeModel(userModel),
            onModelReady: (model) {
              notaryModel = model;
              model.getNotaryOffice();
            },
            builder: (ctx, vm, child) {
              return Container(
                color: Colors.white,
                width: getWidthPx(750),
                alignment: Alignment.center,
                child: vm.notaryList.length > 0
                    ? SmartRefresher(
                    enablePullDown: true,
                    enablePullUp: true,
                    header: HomeRefreshHeader(Colors.black),
                    footer: RefresherFooter(),
                    controller: vm.refreshController,
                    onRefresh: vm.refresh,
                    onLoading: vm.loadMore,
                    child: ListView.builder(
                      // shrinkWrap: true,
                      // physics:NeverScrollableScrollPhysics(),
                        itemCount: vm.notaryList.length,
                        itemBuilder: (ctx, i) {
                          var item = vm.notaryList[i];
                          return InkWell(
                            onTap: () {
                              G.pushNamed(
                                  RoutePaths.NotaryDetail,
                                  arguments: item);
                            },
                            child: Container(
                              padding: EdgeInsets.fromLTRB(
                                  10, 6, 10, 10),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border(
                                    bottom: BorderSide(
                                        width: 1,
                                        color: AppTheme.bg_b)),
                              ),
                              child: Row(
                                children: [
                                  Image.asset(
                                      "lib/assets/images/logo.png",
                                      width: getWidthPx(160)),
                                  Expanded(
                                    child: Padding(
                                      padding:
                                      EdgeInsets.symmetric(
                                          horizontal: 10),
                                      child: Column(
                                        crossAxisAlignment:
                                        CrossAxisAlignment
                                            .start,
                                        children: [
                                          Text(
                                            "${item['notarialName']}",
                                            style: TextStyle(
                                                fontWeight:
                                                FontWeight
                                                    .bold),
                                          ),
                                          Padding(
                                            padding: EdgeInsets
                                                .symmetric(
                                                vertical:
                                                4),
                                            child: Row(
                                              crossAxisAlignment:
                                              CrossAxisAlignment
                                                  .start,
                                              textBaseline:
                                              TextBaseline
                                                  .alphabetic,
                                              children: [
                                                Padding(
                                                  padding:
                                                  EdgeInsets
                                                      .only(
                                                      top: 4),
                                                  child: Image.asset(
                                                      "lib/assets/images/位置.png",
                                                      width:
                                                      12),
                                                ),
                                                Expanded(
                                                    child: Text(
                                                        "${item['address']}",
                                                        maxLines:
                                                        2,
                                                        overflow:
                                                        TextOverflow
                                                            .ellipsis,
                                                        style: TextStyle(
                                                            fontSize:
                                                            14)))
                                              ],
                                            ),
                                          ),
                                          Row(
                                            children: [
                                              Image.asset(
                                                  "lib/assets/images/距离.png",
                                                  width: 12),
                                              Text(
                                                  "距你${double.parse("${item['distance'] ?? 0}") / 1000}km",
                                                  style: TextStyle(
                                                      fontSize:
                                                      14))
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Image.asset(
                                      "lib/assets/images/iPhone.png",
                                      width: 28),
                                  SizedBox(
                                    width: 18,
                                  )
                                ],
                              ),
                            ),
                          );
                        }))
                    : Padding(
                  padding: EdgeInsets.only(top: 20),
                  child: Text("暂无记录"),
                ),
              );
            },
          );
        }));
  }
}
