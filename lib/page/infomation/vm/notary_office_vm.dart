import 'package:amap_location_fluttify/amap_location_fluttify.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/service_api/infomation_api.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class NotaryOfficeModel extends SingleViewStateModel {
  UserViewModel userModel;
  NotaryOfficeModel(this.userModel);
  RefreshController refreshController = RefreshController();
  int currentPage = 1;
  int pageSize = 10;
  List notaryList = [];
  String isOnTap = "";
  bool isShow = false;



  getNotaryOffice() async {
    final status = await Permission.location.request();
    if (status == PermissionStatus.granted) {
      Location location = await AmapLocation.instance.fetchLocation();
      wjPrint("位置信息：-----$location");
      currentPage = 1;
      notaryList.clear();
      Map<String, dynamic> map = {
        "institutionType": "1",
        "currentPage": currentPage,
        "pageSize": 10,
        "distance": "${location.latLng.longitude},${location.latLng.latitude}",
      };
      EasyLoading.show();
      var res = await InformationApi.getSingleton().getNotarial(map,
          errorCallBack: (e) {
        EasyLoading.dismiss();
      });
      EasyLoading.dismiss();
      if (res['code'] == 200) {
        if (res['items'] != null) {
          notaryList = res['items'];
          notifyListeners();
        }
      }
    } else {
      G.showPermissionDialog(str: '定位');
    }
  }

  refresh() async {
    currentPage = 1;
    refreshController.resetNoData();
    final status = await Permission.location.request();
    if (status == PermissionStatus.granted) {
      Location location = await AmapLocation.instance.fetchLocation();
      wjPrint("位置信息：-----${location.latLng.latitude}");
      Map<String, dynamic> map = {
        "institutionType": "1",
        "currentPage": currentPage,
        "pageSize": 10,
        "distance": "${location.latLng.longitude},${location.latLng.latitude}",
      };
      EasyLoading.show();
      InformationApi.getSingleton().getNotarial(map, errorCallBack: (e) {
        EasyLoading.dismiss();
        refreshController.refreshFailed();
        setBusy(false);
      }).then((res) {
        EasyLoading.dismiss();
        notaryList.clear();
        isOnTap = "";
        if (res['code'] == 200) {
          if (res['items'] != null) {
            notaryList = res['items'];
            notifyListeners();
            refreshController.refreshCompleted();
          } else {
            notaryList = [];
            refreshController.refreshCompleted();
            notifyListeners();
          }
        } else {
          refreshController.refreshFailed();
        }
      }).whenComplete(() {
        refreshController.refreshCompleted();
        setBusy(false);
      });
    } else {
      G.showPermissionDialog(str: '定位服务');
    }
  }

  loadMore() async {
    currentPage += 1;
    final status = await Permission.location.request();
    if (status == PermissionStatus.granted) {
      Location location = await AmapLocation.instance.fetchLocation();
      wjPrint("位置信息：-----${location.latLng.latitude}");
      Map<String, dynamic> map = {
        "institutionType": "1",
        "currentPage": currentPage,
        "pageSize": 10,
        "distance": "${location.latLng.longitude},${location.latLng.latitude}",
      };

      InformationApi.getSingleton().getNotarial(map, errorCallBack: (e) {
        currentPage--;
        refreshController.loadFailed();
      }).then((res) {
        if (res['code'] == 200) {
          if (res["items"].isEmpty) {
            refreshController.loadNoData();
          } else {
            notaryList.addAll(res['items']);
            refreshController.loadComplete();
            notifyListeners();
          }
        } else {
          currentPage--;
          refreshController.loadFailed();
        }
      });
    } else {
      G.showPermissionDialog(str: '定位');
    }
  }

  @override
  onCompleted(data) {}

  @override
  Future loadData() {
    return null;
  }
}
