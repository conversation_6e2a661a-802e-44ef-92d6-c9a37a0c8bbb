import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/infomation/vm/notarized_matter_vm.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/global.dart';

import '../../appTheme.dart';

class NotarizedMatterWidget extends StatefulWidget {
  const NotarizedMatterWidget();

  @override
  State<NotarizedMatterWidget> createState() => _NotarizedMatterWidgetState();
}

class _NotarizedMatterWidgetState extends BaseState<NotarizedMatterWidget> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          elevation: 0.0,
          leading: InkWell(
            child: Icon(
              Icons.arrow_back_ios,
              color: Colors.black,
              size: 22,
            ),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          backgroundColor: AppTheme.white,
          centerTitle: true,
          title: Text(
            "公证事项",
            style: TextStyle(color: Colors.black, fontSize: 18),
          )),
      body: ProviderWidget<NotarizedMatterViewModel>(
          builder: (context, vm, child) {
            return vm.treeList.length > 0
                ? ListView.builder(
                    itemCount: vm.treeList.length,
                    itemBuilder: (ctx, i) {
                      var item = vm.treeList[i];
                      return Container(
                        padding: EdgeInsets.fromLTRB(10, 6, 10, 10),
                        color: Colors.white,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                                padding: EdgeInsets.all(10),
                                child: Text(
                                  "${item['name']}",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.bold),
                                )),
                            SizedBox(
                              width: 10,
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 10),
                              child: item['children'].length > 0
                                  ? Padding(
                                      padding:
                                          EdgeInsets.only(left: getWidthPx(10)),
                                      child: Wrap(
                                        spacing: getWidthPx(10),
                                        children: getWidget(item['children']),
                                      ))
                                  : Text("暂无数据"),
                            ),
                          ],
                        ),
                      );
                    })
                : Center(child: Text("暂无记录"),);
          },
          onModelReady: (model) {
            model.getTree();
          },
          model: NotarizedMatterViewModel()),
    );
  }

  List<Widget> getWidget(List info) {
    List<Widget> list = [];
    info.forEach((element) {
      Widget text = InkWell(
          onTap: () {
            G.pushNamed(RoutePaths.MatterList, arguments: element);
          },
          child: Container(
              padding: EdgeInsets.fromLTRB(8, 4, 8, 4),
              margin: EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(4.0)),
                border: Border.all(width: 1, color: AppTheme.Text_min),
              ),
              child: Text(
                "${element['name']}",
                style: TextStyle(color: AppTheme.Text_min),
              )));
      list.add(text);
    });
    return list;
  }
}
