/*
 * @Author: <EMAIL> WSBwsb123!@#
 * @Date: 2023-09-26 09:07:10
 * @LastEditors: <EMAIL> WSBwsb123!@#
 * @LastEditTime: 2023-12-06 18:50:08
 * @FilePath: /sc-remotenotarization-app/lib/page/home/<USER>
 * @Description: 新的首页
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import 'package:flutter/material.dart';
import 'package:notarization_station_app/appTheme.dart';
import 'package:notarization_station_app/base_framework/utils/refresh_helper.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/infomation/vm/community_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/ServiceLocator.dart';
import 'package:notarization_station_app/utils/TelAndSmsService.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../config.dart';

class IndexNew extends StatefulWidget {
  const IndexNew({Key key}) : super(key: key);

  @override
  State<IndexNew> createState() => _IndexNewState();
}

class _IndexNewState extends BaseState<IndexNew> {
  UserViewModel _userModel;

  CommunityModel communityModel;

  TelAndSmsService _service = locator<TelAndSmsService>();

  @override
  Widget build(BuildContext context) {
    return ColorFiltered(
      colorFilter: ColorFilter.mode(
          G.isColorFiltered ? Colors.grey : Colors.transparent,
          BlendMode.color),
      child: Scaffold(
          body: ColoredBox(
              color: AppTheme.bg_e,
              child: Consumer<UserViewModel>(
                builder: (ctx, userModel, child) {
                  return ProviderWidget<CommunityModel>(
                    model: CommunityModel(userModel),
                    onModelReady: (model) {
                      _userModel = userModel;
                      communityModel = model;
                      model.getList(1);
                    },
                    builder: (ctx, vm, child) {
                      return SmartRefresher(
                        enablePullDown: true,
                        enablePullUp: true,
                        header: HomeRefreshHeader(Colors.black),
                        footer: RefresherFooter(),
                        controller: vm.refreshController,
                        onRefresh: vm.refresh,
                        onLoading: vm.loadMore,
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              topBanner,
                              centificateHandleWidget,
                              helpWidget,
                              normalBiddingProcess,
                              itemListWidget('公证事项@3x.png', "公证事项", () {
                                G.pushNamed(RoutePaths.NotarizedMatter);
                              }),
                              itemListWidget('icon_办事指引@3x.png', "办事指引", () {
                                G.pushNamed(RoutePaths.Guide);
                              }),
                              itemListWidget('icon_公证处@3x.png', "公证处", () {
                                G.pushNamed(RoutePaths.NotaryOffice);
                              }),
                              industryWidget,
                              _professionWidget(),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
              ))),
    );
  }

  // 首页banner图
  Widget get topBanner => Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                "lib/assets/images/<EMAIL>",
                width: MediaQuery.of(context).size.width,
                height: (210 / 375) * MediaQuery.of(context).size.width,
                fit: BoxFit.fill,
              ),
              SizedBox(
                height: (50 / 375) * MediaQuery.of(context).size.width,
              ),
            ],
          ),
          Positioned(
              left: 15,
              top: (50 / 375) * MediaQuery.of(context).size.width,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '智慧公证',
                    style: TextStyle(
                        fontSize: 30,
                        color: AppTheme.white,
                        fontWeight: FontWeight.bold),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 5),
                    child: Text(
                      "随时随地 | 自助办证 | 方便快捷 | 安全高效",
                      style: TextStyle(
                          fontSize: 16,
                          color: AppTheme.white,
                          fontWeight: FontWeight.w100),
                    ),
                  )
                ],
              )),
          Positioned(
              bottom: 0,
              child: InkWell(
                onTap: () {
                  _judgeLogin(judgeResult: () {
                    G.pushNamed(RoutePaths.Explain, arguments: 2);
                  }, loginCallBack: () {
                    G.getCurrentState().pushNamedAndRemoveUntil(
                        RoutePaths.LOGIN, (router) => router == null);
                  }, alertResult: () {
                    return ToastUtil.showWarningToast("请先实名认证");
                  });
                },
                child: Stack(
                  alignment: Alignment.centerLeft,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Image.asset(
                        "lib/assets/images/视频连线卡片背景@3x.png",
                        height: (90 / 375) * MediaQuery.of(context).size.width,
                        width: MediaQuery.of(context).size.width - 30,
                        fit: BoxFit.fill,
                      ),
                    ),
                    Positioned(
                        left: 15,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '视频连线公证员办理',
                              style: TextStyle(
                                  fontSize: 20,
                                  color: AppTheme.white,
                                  fontWeight: FontWeight.bold),
                            ),
                            Padding(
                              padding: EdgeInsets.only(top: 5, left: 10),
                              child: Text(
                                "公证员全程在线引导，协助申办",
                                style: TextStyle(
                                    fontSize: 14,
                                    color: AppTheme.white,
                                    fontWeight: FontWeight.w100),
                              ),
                            )
                          ],
                        ))
                  ],
                ),
              )),
        ],
      );

  // 自主公证、预约办证、多方公证
  Widget get centificateHandleWidget => SizedBox(
        height: (MediaQuery.of(context).size.width - 45) / 2 + 30,
        child: ColoredBox(
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            child: Row(children: [
              InkWell(
                onTap: () {
                  _judgeLogin(judgeResult: () {
                    if(_userModel.idCard != null && _userModel.idCard.length == 18){
                      String userBirthDay = G.getBirthDayFromCardId(_userModel.idCard);
                      int userAge = G.getAgeFromBirthday(userBirthDay);
                      if (userAge >= 18){
                        G.pushNamed(RoutePaths.Explain, arguments: 1);
                      } else {
                        ToastUtil.showToast('当前检测到你的年龄未满18周岁暂无法办理当前业务');
                      }
                    } else {
                      G.pushNamed(RoutePaths.Explain, arguments: 1);
                    }
                  }    , loginCallBack: () {
                    G.getCurrentState().pushNamedAndRemoveUntil(
                        RoutePaths.LOGIN, (router) => router == null);
                  }, alertResult: () {
                    return ToastUtil.showWarningToast("请先实名认证");
                  });
                },
                child: Stack(
                  alignment: Alignment.topLeft,
                  children: [
                    Image.asset(
                      "lib/assets/images/自助公证卡片背景@3x.png",
                      height: (MediaQuery.of(context).size.width - 45) / 2,
                      width: (MediaQuery.of(context).size.width - 45) / 2,
                      fit: BoxFit.fill,
                    ),
                    Positioned(
                        left: 15,
                        top: 15,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "自助公证",
                              style: TextStyle(
                                  fontSize: 17,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black),
                            ),
                            const SizedBox(
                              height: 3,
                            ),
                            Text(
                              "自助办理/快速出证",
                              style: TextStyle(
                                  fontSize: 12,
                                  color: AppTheme.dark_grey,
                                  fontWeight: FontWeight.w100),
                            )
                          ],
                        ))
                  ],
                ),
              ),
              const SizedBox(
                width: 15,
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: () {
                      _judgeLogin(judgeResult: () {
                        G.pushNamed(RoutePaths.Appointment);
                      }, loginCallBack: () {
                        G.getCurrentState().pushNamedAndRemoveUntil(
                            RoutePaths.LOGIN, (router) => router == null);
                      }, alertResult: () {
                        return ToastUtil.showWarningToast("请先实名认证");
                      });
                    },
                    child: Stack(
                      alignment: Alignment.topLeft,
                      children: [
                        Image.asset(
                          "lib/assets/images/预约办证卡片背景@3x.png",
                          height:
                              ((MediaQuery.of(context).size.width - 45) / 2 -
                                      16) /
                                  2,
                          width: (MediaQuery.of(context).size.width - 45) / 2,
                          fit: BoxFit.fill,
                        ),
                        Positioned(
                            left: 15,
                            top: 15,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "预约办证",
                                  style: TextStyle(
                                      fontSize: 17,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                Text(
                                  "预约公证员/无需排队",
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.dark_grey,
                                      fontWeight: FontWeight.w100),
                                )
                              ],
                            ))
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  InkWell(
                    onTap: () {
                      _judgeLogin(judgeResult: () {
                        G.pushNamed(RoutePaths.Explain, arguments: 3);
                      }, loginCallBack: () {
                        G.getCurrentState().pushNamedAndRemoveUntil(
                            RoutePaths.LOGIN, (router) => router == null);
                      }, alertResult: () {
                        return ToastUtil.showWarningToast("请先实名认证");
                      });
                    },
                    child: Stack(
                      alignment: Alignment.topLeft,
                      children: [
                        Image.asset(
                          "lib/assets/images/多方公证卡片背景@3x.png",
                          height:
                              ((MediaQuery.of(context).size.width - 45) / 2 -
                                      16) /
                                  2,
                          width: (MediaQuery.of(context).size.width - 45) / 2,
                          fit: BoxFit.fill,
                        ),
                        Positioned(
                            left: 15,
                            top: 15,
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "多方公证",
                                  style: TextStyle(
                                      fontSize: 17,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                Text(
                                  "协议签订/股东会议",
                                  style: TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.dark_grey,
                                      fontWeight: FontWeight.w100),
                                )
                              ],
                            ))
                      ],
                    ),
                  ),
                ],
              )
            ]),
          ),
        ),
      );

  //  申办帮助或业务咨询
  Widget get helpWidget => Padding(
        padding: const EdgeInsets.only(top: 15),
        child: InkWell(
          onTap: () {
            _service.call("4008001820");
          },
          child: ColoredBox(
            color: Colors.white,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15, vertical: 13),
              child: Row(
                children: [
                  Image.asset(
                    "lib/assets/images/icon_提示@3x.png",
                    width: 18,
                    height: 18,
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 15),
                    child: Text("申办帮助或业务咨询：4008001820"),
                  ),
                  const Spacer(),
                  Image.asset(
                    "lib/assets/images/icon_进入@3x.png",
                    width: 18,
                    height: 18,
                  )
                ],
              ),
            ),
          ),
        ),
      );

  // 常规申办流程
  Widget get normalBiddingProcess => Padding(
        padding: const EdgeInsets.only(top: 15),
        child: ColoredBox(
          color: Colors.white,
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 20,
            ),
            child: Column(
              children: [
                Text(
                  "常规申办流程",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black),
                ),
                SizedBox(
                  height: 10,
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 20),
                  width: MediaQuery.of(context).size.width,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const SizedBox(
                            width: 5,
                          ),
                          itemWidget("公证申请@3x.png", " 公证申请 "),
                          Expanded(
                            child: Image.asset(
                              "lib/assets/images/点@3x.png",
                            ),
                          ),
                          itemWidget("谈话及初审@3x.png", "谈话及初审"),
                          Expanded(
                            child: Image.asset(
                              "lib/assets/images/点@3x.png",
                              width: 32,
                            ),
                          ),
                          itemWidget("审查及出证@3x.png", "审查及出证"),
                          Expanded(
                            child: Image.asset(
                              "lib/assets/images/点@3x.png",
                              width: 32,
                            ),
                          ),
                          itemWidget("公证书受领@3x.png", "公证书受领"),
                          const SizedBox(
                            width: 5,
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '  公证申请  ',
                            style: TextStyle(fontSize: 14),
                          ),
                          Text(
                            '谈话及初审',
                            style: TextStyle(fontSize: 14),
                          ),
                          Text(
                            '审查及出证',
                            style: TextStyle(fontSize: 14),
                          ),
                          Text(
                            '公证书受领',
                            style: TextStyle(fontSize: 14),
                          )
                        ],
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );

  // 常规申办的item
  Widget itemWidget(String imageName, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Image.asset(
        'lib/assets/images/$imageName',
        width: 45,
        height: 45,
      ),
    );
  }

  // 首页list的item
  Widget itemListWidget(String imageName, String text, Function onTapClick) {
    return Padding(
      padding: EdgeInsets.only(top: 15),
      child: InkWell(
        onTap: () {
          onTapClick();
        },
        child: ColoredBox(
          color: Colors.white,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 13),
            child: Row(
              children: [
                Image.asset(
                  "lib/assets/images/$imageName", // icon_提示@3x.png
                  width: 23,
                  height: 23,
                ),
                Padding(
                  padding: EdgeInsets.only(left: 15),
                  child: Text(
                    text,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                const Spacer(),
                Image.asset(
                  "lib/assets/images/icon_进入@3x.png",
                  width: 18,
                  height: 18,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 行业动态
  Widget get industryWidget => Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        margin: EdgeInsets.symmetric(vertical: 15),
        child: Row(
          children: [
            Text(
              "行业动态",
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 17),
            ),
            const Spacer(),
          ],
        ),
      );

  /// 行业动态
  Widget _professionWidget() => MediaQuery.removePadding(
      removeTop: true,
      context: context,
      child: ListView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: communityModel.industryList.length,
          itemBuilder: (ctx, i) {
            var item = communityModel.industryList[i];
            List imgList = item["picture"].split(",");
            return InkWell(
              onTap: () {
                _judgeIdentify(alertResult: () {
                  return ToastUtil.showWarningToast("请先实名认证!");
                }, judgeResult: () {
                  G.pushNamed(RoutePaths.News, arguments: item);
                });
              },
              child: Container(
                margin: const EdgeInsets.fromLTRB(0, 0, 0, 10),
                padding: const EdgeInsets.all(15),
                color: Colors.white,
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Image.network(
                        Config.splicingImageUrl(item['picture']),
                        width: getWidthPx(200),
                        height: getWidthPx(200),
                        fit: BoxFit.fill,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Container(
                        height: getWidthPx(200),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text("${item['title'] ?? ''}",
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  style: TextStyle(
                                      fontWeight: FontWeight.w700,
                                      fontSize: 16,
                                      color: Color(0xff4B4B4B))),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              "${item['notarialName'] ?? ''}",
                              style: TextStyle(color: Color(0xff888888)),
                            ),
                            Text(
                              "${item['createDate'] ?? ''}",
                              style: TextStyle(color: Color(0xff888888)),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            );
          }));

  /// 是否实名
  void _judgeIdentify({Function alertResult, Function judgeResult}) {
    if (_userModel.idCard == null || _userModel.idCard == "") {
      ///未实名
      if (alertResult != null) {
        alertResult.call();
      }
    } else {
      /// 实名
      if (judgeResult != null) {
        judgeResult.call();
      }
    }
  }

  /// 是否登录
  /// judgeResult: 登录和实名认证成功后进入回调
  /// loginCallBack：进入登录界面
  /// alertResult
  void _judgeLogin(
      {Function judgeResult, Function loginCallBack, Function alertResult}) {
    /// 未登录
    if (!_userModel.hasUser) {
      if (loginCallBack != null) {
        loginCallBack.call();
      }
      // _judgeIdentify(judgeResult: judgeResult, alertResult: alertResult);
      /// 登录
    } else {
      _judgeIdentify(judgeResult: judgeResult, alertResult: alertResult);
    }
  }
}
