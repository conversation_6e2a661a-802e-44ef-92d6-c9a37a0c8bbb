import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/widget/provider_widget.dart';
import 'package:notarization_station_app/base_framework/widget/stepper_widget.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/home/<USER>/submitOrderList_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:provider/provider.dart';

import '../../appTheme.dart';

class SubmitOrderListPage extends StatefulWidget {
  final arguments;
  const SubmitOrderListPage({Key key, this.arguments}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return SubmitOrderListState();
  }
}

class SubmitOrderListState extends BaseState<SubmitOrderListPage>
    with AutomaticKeepAliveClientMixin {
  SubmitOrderListModel submitViewModel;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return WillPopScope(
      onWillPop: ()async{
        G.pushNamed(RoutePaths.HomeIndex);
        return Future.value(true);
      },
      child: Scaffold(
          appBar: AppBar(
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.App_bar,
                ),
              ),
            ),
            centerTitle: true,
            title: Text(
              "提交订单",
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
            leading: IconButton(
                icon: Icon(
                  Icons.navigate_before,
                  color: Colors.white,
                  size: 30,
                ),
                onPressed: () {
                  G.pushNamed(RoutePaths.HomeIndex);
                }),
          ),
          body: Consumer<UserViewModel>(
            builder: (ctx, userModel, child) {
              return ProviderWidget<SubmitOrderListModel>(
                model: SubmitOrderListModel(userModel, widget.arguments),
                onModelReady: (model) {
                  submitViewModel = model;
                  model.loadData();
                },
                builder: (ctx, homeModel, child) {
                  return SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        SizedBox(
                          height: getHeightPx(40),
                        ),
                        Container(
                          width: getWidthPx(700),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.circular(getWidthPx(10)),
                              boxShadow: <BoxShadow>[
                                //设置阴影
                                new BoxShadow(
                                  color: Colors.grey, //阴影颜色
                                  blurRadius: getWidthPx(5), //阴影大小
                                ),
                              ]),
                          child: Column(
                            children: <Widget>[
                              SizedBox(
                                height: getHeightPx(20),
                              ),
                              Padding(
                                padding: EdgeInsets.only(left: getWidthPx(30)),
                                child: Row(
                                  children: <Widget>[
                                    Icon(
                                      Icons.monetization_on,
                                      color: Colors.orange,
                                      size: 20,
                                    ),
                                    Text(
                                      "公证事项明细",
                                      style: TextStyle(
                                          fontSize: 15,
                                          fontWeight: FontWeight.w700),
                                    )
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: getHeightPx(20),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  itemCount: widget
                                      .arguments["notarizationMatters"].length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    var item =
                                        widget.arguments["notarizationMatters"]
                                            [index];
                                    return Column(
                                      children: <Widget>[
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: <Widget>[
                                            Container(
                                              width:200,
                                              margin: EdgeInsets.only(
                                                bottom: getWidthPx(10),
                                              ),
                                              child: Text(
                                                item["name"] + "×1",
                                                style: TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.black),
                                              ),
                                            ),
                                            Container(
                                              margin: EdgeInsets.only(
                                                bottom: getWidthPx(10),
                                              ),
                                              child: Text(
                                                "￥" +
                                                    item["price"].toString() +
                                                    "元",
                                                style: TextStyle(
                                                    fontSize: 14,
                                                    color: Colors.black),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: getHeightPx(10),
                                        ),
                                      ],
                                    );
                                  },
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(
                                        bottom: getWidthPx(10),
                                      ),
                                      child: Text(
                                        "公证书" +
                                            "×" +
                                            submitViewModel.number.toString(),
                                        style: TextStyle(
                                            fontSize: 14, color: Colors.black),
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                        bottom: getWidthPx(10),
                                      ),
                                      child: Text(
                                        "￥" +
                                            (20 *
                                                    int.parse(
                                                        submitViewModel.number))
                                                .toString() +
                                            ".0" +
                                            "元",
                                        style: TextStyle(
                                            fontSize: 14, color: Colors.black),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: getHeightPx(20),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(
                                        bottom: getWidthPx(10),
                                      ),
                                      child: Text(
                                        "总费用",
                                        style: TextStyle(
                                            fontSize: 16,
                                            color: Colors.black,
                                            fontWeight: FontWeight.w500),
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                        bottom: getWidthPx(10),
                                      ),
                                      child: Text(
                                        "￥" +
                                            (submitViewModel.totalMoney +
                                                    20 *
                                                        int.parse(
                                                            submitViewModel
                                                                .number))
                                                .toString() +
                                            "元",
                                        style: TextStyle(
                                            fontSize: 16, color: Colors.black),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: getHeightPx(20),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: getHeightPx(60),
                        ),
                        InkWell(
                          onTap: () {
                            // G.pushNamed(RoutePaths.ListInformation,arguments: {"orderId":widget.arguments["orderId"],"totalMoney":submitViewModel.totalMoney});
                            Navigator.pushNamed(
                                context, RoutePaths.ZaiXianDetail, arguments: {
                              "data": {"unitGuid": widget.arguments["orderId"]}
                            }).then((value) =>
                                submitViewModel.getOneNotarizationData());
                          },
                          child: Container(
                              alignment: Alignment.center,
                              margin: EdgeInsets.only(
                                  top: getWidthPx(50),
                                  left: getWidthPx(40),
                                  right: getWidthPx(40)),
                              padding: EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.all(
                                      Radius.circular(getWidthPx(40))),
                                  color: Colors.white,
                                  border:
                                      Border.all(color: AppTheme.themeBlue)),
                              child: Text('查看订单',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: AppTheme.themeBlue))),
                        ),
                        // SizedBox(
                        //   height: getHeightPx(60),
                        // ),
                        // homeModel.isShow &&
                        //         NotaryData.notaryId ==
                        //             "666c2421-b2e1-4076-80aa-8e63cf287de4"
                        //     ? InkWell(
                        //         onTap: () {
                        //           wjPrint("点击了立即付款");
                        //           submitViewModel.goPay();
                        //         },
                        //         child: Container(
                        //             alignment: Alignment.center,
                        //             margin: EdgeInsets.only(
                        //                 top: getWidthPx(50),
                        //                 left: getWidthPx(40),
                        //                 right: getWidthPx(40)),
                        //             padding: EdgeInsets.all(10),
                        //             decoration: BoxDecoration(
                        //               borderRadius:
                        //                   BorderRadius.all(Radius.circular(5)),
                        //               color: AppTheme.themeBlue,
                        //             ),
                        //             child: Text('立即付款',
                        //                 style: TextStyle(
                        //                     fontSize: 16,
                        //                     color: AppTheme.nearlyWhite))),
                        //       )
                        //     : SizedBox(),
                        // NotaryData.notaryId !=
                        //         "666c2421-b2e1-4076-80aa-8e63cf287de4"
                        //     ?
                        Offstage(
                          offstage: !submitViewModel.isShowOfflinePay(),
                          child: InkWell(
                            onTap: () {
                              submitViewModel.doSetState(context);
                            },
                            child: Container(
                                alignment: Alignment.center,
                                margin: EdgeInsets.only(
                                    top: getWidthPx(50),
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                padding: EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(5)),
                                  color: AppTheme.themeBlue,
                                ),
                                child: Text('线下支付',
                                    style: TextStyle(
                                        fontSize: 16,
                                        color: AppTheme.white))),
                          ),
                        ),
                        Offstage(
                          offstage: !submitViewModel.isShowOnlinePay(),
                          child: InkWell(
                            onTap: () {
                              submitViewModel.goPay(context);
                            },
                            child: Container(
                                alignment: Alignment.center,
                                margin: EdgeInsets.only(
                                    top: getWidthPx(50),
                                    left: getWidthPx(40),
                                    right: getWidthPx(40)),
                                padding: EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(5)),
                                  color: AppTheme.themeBlue,
                                ),
                                child: Text('线上支付',
                                    style: TextStyle(
                                        fontSize: 16,
                                        color: AppTheme.nearlyWhite))),
                          ),
                        ),
                        Container(
                          width: getWidthPx(700),
                          margin: EdgeInsets.only(
                              top: getHeightPx(30), bottom: getHeightPx(60)),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(getWidthPx(10)),
                            boxShadow: <BoxShadow>[
                              //设置阴影
                              new BoxShadow(
                                color: Colors.grey, //阴影颜色
                                blurRadius: getWidthPx(5), //阴影大小
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Padding(
                                padding: EdgeInsets.only(
                                    left: getWidthPx(20),
                                    bottom: getWidthPx(30),
                                    top: getWidthPx(30)),
                                child: Row(
                                  children: [
                                    Image.asset(
                                      'lib/assets/images/result_list.png',
                                      width: getWidthPx(34),
                                      height: getWidthPx(34),
                                    ),
                                    SizedBox(
                                      width: getWidthPx(20),
                                    ),
                                    const Text(
                                      '后续流程',
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 14),
                                    )
                                  ],
                                ),
                              ),
                              StepsVehticalWidget(
                                data: [
                                  '提交订单',
                                  '支付完成，公证员审查信息后联系',
                                  '审查通过，分配公证员办理',
                                  '根据公证员提示存放原件至指定位置',
                                  '制证中',
                                  '制证完成，待领证'
                                ],
                              ),
                              const SizedBox(
                                height: 15,
                              ),
                            ],
                          ),
                        ),
                        // : SizedBox(),
                        SizedBox(
                          height: getHeightPx(30),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          )),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
