import 'dart:async';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/home_api.dart';

import '../../../base_framework/view_model/single_view_state_model.dart';

class PayViewModel extends SingleViewStateModel {
  bool isEnable = true;

  /// 回调地址
  String callBackUrl;

  /// 支付类型 1、公证费用 2、补缴费用
  int payLogo;

  /// 订单主键
  String unitGuid;

  /// 订单编号
  String orderNo;

  /// 保存临时变量
  String bnkPyOrdrNo;

  String cstmPyOrdrNo;

  String pyUrl;

  // Timer myTimer;

  UserViewModel userViewModel;

  /// 设置轮巡时间
  // int _totalSecond = 300;

  // PayViewModel(String url,int logo,String id){
  //   callBackUrl = url;
  //   payLogo = logo;
  //   unitGuid = id;
  // }

  /// 下单
  void addOrder(BuildContext context) {
    isEnable = false;
    notifyListeners();
    EasyLoading.show();
    String source = '';
    if (Platform.isIOS) {
      source = '2';
    } else if (Platform.isAndroid) {
      source = '1';
    }
    Map<String, dynamic> data = {
      "callBackUrl": callBackUrl,
      'payLogo': payLogo,
      "unitGuid": unitGuid,
      'pyChnlCd': "2",
      'paySources': source
    };
    HomeApi.getSingleton().placeOrder(data, errorCallBack: (e) {
      isEnable = true;
      EasyLoading.dismiss();
      notifyListeners();
    }).then((value) {
      isEnable = true;
      EasyLoading.dismiss();
      notifyListeners();
      if (value["code"] == 200) {
        if (value['data']['pyUrl'] != null) {
          cstmPyOrdrNo = value['data']['cstmPyOrdrNo'];
          bnkPyOrdrNo = value['data']['bnkPyOrdrNo'];
          // Ccbpay.payWitWXApi(mainServiceHttp: value["data"]["pyUrl"])
          //     .then((value) {
          //   wjPrint("支付结果回调结果：$value");
          // });
          // Navigator.pushNamed(context, RoutePaths.webView,
          //     arguments: {"url": value["data"]["pyUrl"]}).then((value) {
          //   getPayStatus(getData(context));
          // });
        } else {
          ToastUtil.showErrorToast("请选择支付方式");
        }
      } else if (value["code"] == 11045) {
        ToastUtil.showSuccessToast(value['message']);
        userViewModel.currentIndex = 1;
        Navigator.pushNamedAndRemoveUntil(
            context, RoutePaths.HomeIndex, (route) => false);
      } else {
        ToastUtil.showErrorToast(value['message']);
      }
    });
  }

  /// 数币支付下单
  void addOrderByShuBi(BuildContext context) {
    isEnable = false;
    notifyListeners();
    EasyLoading.show();
    String source = '';
    if (Platform.isIOS) {
      source = '2';
    } else if (Platform.isAndroid) {
      source = '1';
    }
    Map<String, dynamic> data = {
      "callBackUrl": "/index",
      'payLogo': payLogo,
      "unitGuid": unitGuid,
      'paySources': source
    };
    HomeApi.getSingleton().ccBisPlaceOrder(data, errorCallBack: (e) {
      isEnable = true;
      EasyLoading.dismiss();
      notifyListeners();
    }).then((value) {
      isEnable = true;
      EasyLoading.dismiss();
      notifyListeners();
      if (value["code"] == 200) {
        if (value['data'] != null) {
          Navigator.pushNamed(context, RoutePaths.webView, arguments: {
            "url": value["data"]["payUrl"],
            'orderNo': orderNo,
            'source': 1
          });
        } else {
          ToastUtil.showErrorToast("获取得地址链接为空");
        }
      } else if (value["code"] == 11045) {
        ToastUtil.showSuccessToast(value['message']);
        userViewModel.currentIndex = 1;
        Navigator.pushNamedAndRemoveUntil(
            context, RoutePaths.HomeIndex, (route) => false);
      } else {
        ToastUtil.showErrorToast(value['message']);
      }
    });
  }

  // /// 轮巡查询订单状态
  // void getPayStatus(BuildContext context, {Function eventCall}) {
  //   if (myTimer != null) {
  //     myTimer.cancel();
  //   }
  //   _totalSecond = 300;
  //   myTimer = Timer.periodic(Duration(seconds: 3), (timer) {
  //     _totalSecond = _totalSecond - 3;
  //     if (_totalSecond <= 0) {
  //       myTimer.cancel();
  //       ToastUtil.showErrorToast("支付超时,请重新发起支付");
  //       G.pop();
  //       return;
  //     } else {
  //       eventCall(context);
  //     }
  //   });
  // }

  // /// 数币支付订单状态查询
  // getShuBiData(BuildContext context) async {
  //   var map = {
  //     "orderNo": orderNo,
  //   };
  //   HomeApi.getSingleton()
  //       .queryShuBiOrder(map, errorCallBack: (e) {}, checkNet: false)
  //       .then((value) {
  //     wjPrint("33333333333333$value");
  //     if (value["code"] == 200) {
  //       // payStatus	1、成功 2、未支付 3、支付超时 4、支付失败
  //       if (value['data'] != null &&
  //           value['data']['payStatus'] != null &&
  //           value['data']['payStatus'] == '1') {
  //         ToastUtil.showSuccessToast("支付成功");
  //         userViewModel.currentIndex = 1;
  //         Navigator.pushNamedAndRemoveUntil(
  //             context, RoutePaths.HomeIndex, (route) => false);
  //         myTimer.cancel();
  //       } else if (value['data'] != null &&
  //           value['data']['payStatus'] != null &&
  //           value['data']['payStatus'] == '2') {
  //         ToastUtil.showSuccessToast("未支付");
  //         myTimer.cancel();
  //       } else if (value['data'] != null &&
  //           value['data']['payStatus'] != null &&
  //           value['data']['payStatus'] == '3') {
  //         ToastUtil.showSuccessToast("支付超时");
  //         myTimer.cancel();
  //       } else {
  //         ToastUtil.showSuccessToast("支付失败");
  //         myTimer.cancel();
  //       }
  //     }
  //   });
  // }

  // getData(BuildContext context) async {
  //   var map = {
  //     "bnkPyOrdrNo": bnkPyOrdrNo,
  //     'cstmPyOrdrNo': cstmPyOrdrNo,
  //   };
  //   HelperApi.getSingleton()
  //       .queryOrderInfo(map, errorCallBack: (e) {}, checkNet: false)
  //       .then((value) {
  //     wjPrint("33333333333333$value");

  //     // ordrStCd  订单状态代码(1待缴费 2成功 3失败 4全部退费 5部分退费 6失效 （一直未支付，超时过期，作为失效订单） 9取消 a 处理中 （支付成功，待通知代收付中） b 待冲正（冲正定时自任务 将待冲正的支付订单，插入退款表） c 待系统退费（冲正定时自任务 将支付订单订单状态改成c待系统退费状态后进行处理） d-部分成功 g-待人工处理 h-撤销中 i-已关闭)
  //     if (value["code"] == 200) {
  //       if (value['data'] != null &&
  //           value['data']['ordrStCd'] != null &&
  //           value['data']['ordrStCd'] == '2') {
  //         ToastUtil.showSuccessToast("支付成功");
  //         userViewModel.currentIndex = 1;
  //         Navigator.pushNamedAndRemoveUntil(
  //             context, RoutePaths.HomeIndex, (route) => false);
  //         myTimer.cancel();
  //       } else if (value['data'] != null &&
  //           value['data']['ordrStCd'] != null &&
  //           value['data']['ordrStCd'] == '1') {
  //         ToastUtil.showSuccessToast("待缴费");
  //       } else {
  //         ToastUtil.showSuccessToast("失败");
  //       }
  //     }
  //   });
  // }

  @override
  Future loadData() {
    throw UnimplementedError();
  }

  @override
  onCompleted(data) {
    throw UnimplementedError();
  }
}
