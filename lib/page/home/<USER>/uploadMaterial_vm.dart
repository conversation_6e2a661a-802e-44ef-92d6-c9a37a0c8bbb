import 'dart:collection';
import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/base_framework/widget/photo_view/photo_view_gallery.dart';
import 'package:notarization_station_app/base_framework/widget/release_widget.dart';
import 'package:notarization_station_app/config.dart';
import 'package:notarization_station_app/page/home/<USER>/bottomsheet/uploadMaterial_bottomsheet_vm.dart';
import 'package:notarization_station_app/page/home/<USER>/dialog/uploadMaterial_dialog_vm.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/service_api/home_api.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:webview_flutter_plus/webview_flutter_plus.dart';

class UploadMaterialModel extends SingleViewStateModel {
  final UserViewModel userViewModel;
  final arguments;

  String selectType = "自取";
  ReleaseCon otherList = new ReleaseCon();
  List<String> resList = [];
  List<String> idList = [];
  List<String> otherMaterialList = [];
  List huKouImgLists = [];
  List identityImgLists = [];
  List otherImgLists = [];
  String toDay;
  List notaryItems = [];
  String orderId;
  TextEditingController materialName;
  String annexFileList; //上传材料成功返回的id
  List materialIdList = [];
  List materialList = [];
  List uploadMaterialLists = [];
  Map materialData;
  List imgIdList = []; //上传材料保存传的参数里面的图片id
  List upLoadData = []; //上传材料保存传的参数
  WebViewPlusController _controller;
  List previewImgList = []; //预览的图片数组
  double videoHeight;
  int showOneImg = 1;
  bool alreadyRead = false;
  bool clickNext = false; //点击下一步
  var jiaMiBao;
  List upMaterialList = []; //上传材料需要的数组
  Map<String, List<String>> imageMap = new HashMap();

  bool isEdite = true;
  bool isName = true;

  bool bottomBtnEnable = true;

  UploadMaterialModel(this.userViewModel, this.arguments) {
    materialName = TextEditingController();
  }

  showSignature(String name, String idCard, String tyKeyword, String offSet,
      BuildContext context) {
    // EasyLoading.show();
    String url =
        '${Config.caUrl}?name=$name&idCard=$idCard&tyKeyword=$tyKeyword&offSet=$offSet&terminalType=flutter';
    if (Platform.isIOS) {
      url = Uri.encodeFull(
          '${Config.caUrl}?name=$name&idCard=$idCard&tyKeyword=$tyKeyword&offSet=$offSet&terminalType=flutter');
    }
    _showCustomModalBottomSheet(context, name);
    // dialog(name);
  }

  dialog(String name) {
    // 对话框
    showDialog(
        context: G.getCurrentState().overlay.context,
        builder: (ctx) {
          wjPrint('=========================arguments: ' + arguments.toString());
          wjPrint('=========================orderId: ' +
              arguments['orderId'].toString());
          wjPrint('=========================notarizationMatters: ' +
              arguments['notarizationMatters'].toString());
          wjPrint('=========================unitGuid: ' +
              arguments['notarizationMatters'][0]['unitGuid']);
          wjPrint('=========================notaryForm: ' +
              arguments['notaryForm'].toString());
          return DemoPage(
              orderId: arguments['orderId'],
              idCard: userViewModel.idCard,
              name: name,
              unitGuid: arguments['notarizationMatters'][0]['unitGuid'],
              notarizationMatters: arguments['notarizationMatters'],
              notaryForm: arguments["notaryForm"]);

          // return WebViewPlus(
          //   javascriptMode: JavascriptMode.unrestricted,
          //   onWebViewCreated: (controller) {
          //     controller.loadUrl(url,headers: {});
          //   },
          //   javascriptChannels: <JavascriptChannel>[
          //     JavascriptChannel(
          //         name: "share",
          //         onMessageReceived: (JavascriptMessage message) {
          //           if(message.message!=null){
          //             G.pop();

          //             Map msg =  json.decode(message.message);
          //             List arr = [];
          //             arr.add(msg['base64']);
          //             Map<String,Object> map1 = {"files": arr,"idCard":'111'};
          //             HomeApi.getSingleton().uploadImg(map1).then((res){
          //               if(res!=null){
          //                 if(res['code']==200){
          //                   log(msg['encDataFilePath'].toString());
          //                   EasyLoading.show();
          //                   jiaMiBao = jsonDecode(msg['encDataFilePath']);
          //                   doSetSignName(res['item'][0]['filePath']);
          //                   isName = false;
          //                   notifyListeners();
          //                 }
          //               }
          //             });
          //           }
          //         }
          //     ),
          //   ].toSet(),
          //   onPageFinished: (url) {
          //     EasyLoading.dismiss();
          //   },
          // );
        });
  }

// 底部弹窗签名
  Future<int> _showCustomModalBottomSheet(context, String name) async {
    return showModalBottomSheet<int>(
      enableDrag: false,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return SingleChildScrollView(
            child: Container(
          clipBehavior: Clip.antiAlias,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(20.0),
              topRight: const Radius.circular(20.0),
            ),
          ),
          // height: MediaQuery.of(context).size.height / 1.3,
          // height: 500,
          child: Column(children: [
            SizedBox(
              height: 50,
              child: Stack(
                textDirection: TextDirection.rtl,
                children: [
                  Center(
                    child: Text(
                      '签名区域(请逐字签署)',
                      style: TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 16.0),
                    ),
                  ),
                  IconButton(
                      icon: Icon(Icons.close),
                      onPressed: () {
                        SystemChrome.setPreferredOrientations([
                          DeviceOrientation.portraitUp,
                          DeviceOrientation.portraitDown
                        ]);
                        Navigator.of(context).pop();
                      }),
                ],
              ),
            ),
            Divider(height: 1.0),
            qmDemoPage1(
                orderId: arguments['orderId'],
                idCard: userViewModel.idCard,
                name: name,
                unitGuid: arguments['notarizationMatters'][0]['unitGuid'],
                notarizationMatters: arguments['notarizationMatters'],
                notaryForm: arguments["notaryForm"])
          ]),
        ));
      },
    );
  }

  void doSetSignName(String path) async {
    //签字
    var map = {
      "unitGuid": arguments["orderId"],
      "signName": Config.splicingImageUrl(path),
      "encDataFilePath": jiaMiBao,
    };
    wjPrint("++++++++++++++$map");
    HomeApi.getSingleton().doSetSignName(map,errorCallBack: (e){
      EasyLoading.dismiss();
      G.pop();
    }).then((value) {
      EasyLoading.dismiss();
      if (value["code"] == 200) {
        ToastUtil.showSuccessToast("签字成功！");
        G.pushNamed(RoutePaths.SubmitOrderList, arguments: {
          "notarizationMatters": arguments["notarizationMatters"],
          "orderId": arguments["orderId"],
          "notaryForm": arguments["notaryForm"]
        });
      } else {
        ToastUtil.showErrorToast(value["msg"]);
        G.pop();
      }
    });
  }

  //上传材料图片
  Future updateOtherImage(id, MultipartFile file) async {
    var res = await HomeApi.getSingleton().uploadPictures(file,errorCallBack: (e){
      EasyLoading.dismiss();
    });
    if (res['code'] == 200) {
      if (!imageMap.containsKey(id)) {
        imageMap[id] = [];
      }
      imageMap[id].add(res["item"]['unitGuid']);
    } else {
      EasyLoading.dismiss();
      ToastUtil.showErrorToast("图片上传失败");
    }
  }

  void getUpLoadMaterialName() async {
    setBusy(true);
    arguments["notarizationMatters"].forEach((data) {
      materialIdList.add(data["unitGuid"]);
    });

    var map = {"unitGuid": materialIdList.join(",")};
    HomeApi.getSingleton().getUpLoadMaterialName(map,
    errorCallBack: (e){
      setBusy(false);
    }).then((value) {
      setBusy(false);
      if (value["code"] == 200) {
        value["items"].forEach((data) {
          materialList.add(data);
          materialData = {
            "unitGuid": data["unitGuid"],
            "materialName": data["name"],
            "annexFileLists": "",
            "notaryItemId": data["notaryItemModelId"],
            "orderId": arguments["orderId"],
          };
          upLoadData.add(materialData);
        });
      }
    });
  }

  void previewFile() async {
    EasyLoading.show();
    upLoadData.forEach((element) {
      if (imageMap.containsKey(element["unitGuid"])) {
        element["annexFileLists"] = imageMap[element["unitGuid"]];
      } else {
        element["annexFileLists"] = [];
      }
    });
    var map = {
      "items": JsonUtil.encodeObj(upLoadData),
      "orderId": arguments["orderId"],
    };
    wjPrint("预览参数$map");
    bottomBtnEnable = false;
    notifyListeners();
    HomeApi.getSingleton().previewFile(map,errorCallBack: (e){
      EasyLoading.dismiss();
      bottomBtnEnable = true;
      notifyListeners();
    }).then((value) {
      EasyLoading.dismiss();
      bottomBtnEnable = true;
      notifyListeners();
      if (value["code"] == 200) {
        isEdite = false;
        notifyListeners();
        ToastUtil.showSuccessToast("订单提交成功！");
        value["pdfImgPaths"].forEach((data) {
          previewImgList.add(Config.splicingImageUrl(data));
        });
        showImg();
      } else {
        ToastUtil.showErrorToast(value["msg"]);
      }
    });
  }

  showImg() {
    showDialog(
        context: G.getCurrentState().overlay.context,
        builder: (ctx) {
          return PhotoViewGalleryScreen(
              height: videoHeight,
              isBase64: false,
              onWillPop: () {
                return Future.value(false);
              },
              onTop: () {
                G.pop();
               Navigator.pushNamed(ctx, RoutePaths.faceCompareIdentifyWidget,arguments: {'orderId':arguments["orderId"]}).then((value){
                 alreadyRead = true;
                 notifyListeners();
               });
              },
              images: previewImgList,
              //传入图片list
              index: 0,
              //传入当前点击的图片的index
              heroTag: "1");
        });
  }

  void setSelectType(String value) {
    selectType = value;
    notifyListeners();
  }

  @override
  onCompleted(data) {}

  @override
  Future loadData() {
    getUpLoadMaterialName();
    DateTime now = new DateTime.now();
    this.toDay = "${now.year}-${now.month}-${now.day}";
    this.arguments["notarizationMatters"].forEach((item) {
      this.notaryItems.add(item);
    });
    return null;
  }
}
