/*
 * @Author: <EMAIL> wsb5211123
 * @Date: 2024-04-01 10:39:09
 * @LastEditors: <EMAIL> wsb5211123
 * @LastEditTime: 2024-07-15 15:11:50
 * @FilePath: /sc-remotenotarization-app/lib/page/login/entity/userInfo.dart
 * @Description: 
 * 
 * Copyright (c) 2024 by ${git_name_email}, All Rights Reserved. 
 */
class UserInfoEntity {
  String birthday;
  String headIcon;
  String address;
  int gender;
  String idCard;
  String registeAddress;
  String roleName;
  String userName;
  String token;
  String mobile;
  bool restPwStatus;

  UserInfoEntity(
      {this.birthday,
      this.headIcon,
      this.address,
      this.gender,
      this.idCard,
      this.registeAddress,
      this.roleName,
      this.userName,
      this.token,
      this.mobile,
      this.restPwStatus});

  UserInfoEntity.fromJson(Map<String, dynamic> json) {
    birthday = json['birthday'];
    headIcon = json['headIcon'];
    address = json['address'];
    gender = json['gender'];
    idCard = json['idCard'];
    registeAddress = json['registeAddress'];
    roleName = json['roleName'];
    userName = json['userName'];
    token = json['token'];
    mobile = json['mobile'];
    restPwStatus = json['restPwStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['birthday'] = this.birthday;
    data['headIcon'] = this.headIcon;
    data['address'] = this.address;
    data['gender'] = this.gender;
    data['idCard'] = this.idCard;
    data['registeAddress'] = this.registeAddress;
    data['roleName'] = this.roleName;
    data['userName'] = this.userName;
    data['token'] = this.token;
    data['mobile'] = this.mobile;
    data['restPwStatus'] = this.restPwStatus;
    return data;
  }
}
