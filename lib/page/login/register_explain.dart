import 'package:flutter/material.dart';


class RegisterExplainWidget extends StatelessWidget {
  const RegisterExplainWidget({Key key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          '注册说明',
          style: TextStyle(
            color: Colors.white
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 15),
              child: RichText(text: TextSpan(
                text: "问题一:  ",
                style: TextStyle(
                  fontSize: 13,
                  color: Color(0xff02A7F0)
                ),
                children: [
                  TextSpan(
                    text: '境外手机号可以注册吗？',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 13
                    )
                  )
                ]
              )),
            ),
            RichText(text: TextSpan(
                text: "答:  ",
                style: TextStyle(
                    fontSize: 13,
                    color: Color(0xff999999)
                ),
                children: [
                  TextSpan(
                      text: '可以。选择相应国家地区的区号，输入手机号码，获取验证码即可注册。',
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 13
                      )
                  )
                ]
            )),
            Padding(
              padding: const EdgeInsets.only(top: 15),
              child: RichText(text: TextSpan(
                  text: "问题二:  ",
                  style: TextStyle(
                      fontSize: 13,
                      color: Color(0xff02A7F0)
                  ),
                  children: [
                    TextSpan(
                        text: '收不到验证码，怎么办？',
                        style: TextStyle(
                            color: Colors.black,
                            fontSize: 13
                        )
                    )
                  ]
              )),
            ),
            RichText(text: TextSpan(
                text: "答:  ",
                style: TextStyle(
                    fontSize: 13,
                    color: Color(0xff999999)
                ),
                children: [
                  TextSpan(
                      text: '点击「联系公证员」按钮，输入您的手机号码，根据提示联系公证员即可启用账号。',
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 13
                      )
                  )
                ]
            )),
            Padding(
              padding: const EdgeInsets.only(top: 15),
              child: RichText(text: TextSpan(
                  text: "问题三:  ",
                  style: TextStyle(
                      fontSize: 13,
                      color: Color(0xff02A7F0)
                  ),
                  children: [
                    TextSpan(
                        text: '遇到其他问题，如何联系公证员？',
                        style: TextStyle(
                            color: Colors.black,
                            fontSize: 13
                        )
                    )
                  ]
              )),
            ),
            RichText(text: TextSpan(
                text: "答:  ",
                style: TextStyle(
                    fontSize: 13,
                    color: Color(0xff999999)
                ),
                children: [
                  TextSpan(
                      text: '公证员联系方式：4008001820',
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 13
                      )
                  )
                ]
            )),
          ],
        ),
      ),
    );
  }
}
