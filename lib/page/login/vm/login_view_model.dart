import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/base_framework/view_model/single_view_state_model.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/service_api/account_api.dart';
import 'package:notarization_station_app/utils/ServiceLocator.dart';
import 'package:notarization_station_app/utils/TelAndSmsService.dart';

import '../../../appTheme.dart';
import '../../../config.dart';
import '../../../routes/router.dart';
import '../../../utils/global.dart';
import '../entity/userInfo.dart';

class LoginViewModel extends SingleViewStateModel {
  TextEditingController nameController = TextEditingController();
  TextEditingController passController = TextEditingController();
  UserViewModel userViewModel;
  LoginViewModel(this.userViewModel);
  bool loading = false;
  int count = 10;
  bool passwordShow = false;
  TelAndSmsService service = locator<TelAndSmsService>();

  login(BuildContext context) {
    if (nameController.text.isEmpty || passController.text.isEmpty) {
      ToastUtil.showWarningToast("用户名或密码不能为空！");
      return;
    }
    loading = true;
    notifyListeners();
    Map<String, String> map = {
      "loginName": nameController.text,
      "password": generateMd5(passController.text),
      "source": "1",
      "type": "2"
    };
    EasyLoading.show(status: "登录中...");
    AccountApi.getSingleton().getLogin(map, errorCallBack: (e) {
      loading = false;
      EasyLoading.dismiss();
      notifyListeners();
    }).then((data) {
      if (data != null) {
        if (data["code"] == 200 && data["items"]['role'].length != 0) {
          SpUtil.remove("lastUserId");
          AccountApi.getSingleton().getUserInfo(
              {'roleId': data["items"]['role'][0]['roleId'], 'device': 'app'},
              data["items"]['token'], errorCallBack: (e) {
            loading = false;
            notifyListeners();
          }).then((res) {
            EasyLoading.dismiss();
            loading = false;
            notifyListeners();
            if (res["code"] == 200) {
              res['data']['token'] = data["items"]['token'];
              UserInfoEntity user = UserInfoEntity.fromJson(res['data']);
              if (user.restPwStatus) {
                ToastUtil.showSuccessToast("当前密码过于简单，请修改密码后再次登录");
                bool isAbroad = false;
                if (SpUtil.getString(Config.lastUserPhoneNumber) != null &&
                    SpUtil.getString(Config.lastUserPhoneNumber).isNotEmpty) {
                  if (RegExp("^1[3456789]\\d{9}\$")
                      .hasMatch(SpUtil.getString(Config.lastUserPhoneNumber))) {
                    isAbroad = false;
                  } else {
                    isAbroad = true;
                  }
                  Navigator.pushNamed(context, RoutePaths.ModifyPsd,
                      arguments: {
                        'isAbroad': isAbroad,
                        'phoneNumber':
                            SpUtil.getString(Config.lastUserPhoneNumber),
                        'title': "找回密码"
                      });
                } else {
                  Navigator.pushNamed(context, RoutePaths.ModifyPsd,
                      arguments: {
                        'isAbroad': isAbroad,
                        'phoneNumber': '',
                        'title': " 找回密码"
                      });
                }
              } else {
                ToastUtil.showSuccessToast("登录成功！");
                userViewModel.saveUser(user);
                G.getCurrentState().pushNamedAndRemoveUntil(
                    RoutePaths.HomeIndex, (Route route) => false);
              }
            } else {
              ToastUtil.showWarningToast("登录失败，请稍后再试");
            }
          });
        } else if (data['code'] == 11012) {
          EasyLoading.dismiss();
          loading = false;
          showAccountForbiddenAlert(context);
        } else {
          EasyLoading.dismiss();
          loading = false;
          ToastUtil.showWarningToast(data["message"] ?? data['msg']);
        }
      } else {
        ToastUtil.showErrorToast("登录失败，稍后再试");
      }
      notifyListeners();
    });
  }

  // // 修改密码弹框提示
  // showChangePasswordAlert(BuildContext context) {
  //   showDialog(
  //       context: context,
  //       builder: (context) {
  //         return Material(
  //             color: Colors.black.withAlpha(100),
  //             child: Align(
  //                 alignment: Alignment.center,
  //                 child: Container(
  //                   margin: EdgeInsets.symmetric(horizontal: 30),
  //                   decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.circular(10),
  //                     color: Colors.white,
  //                   ),
  //                   child: Column(
  //                     mainAxisSize: MainAxisSize.min,
  //                     children: [
  //                       Padding(
  //                         padding: const EdgeInsets.symmetric(vertical: 10),
  //                         child: Text(
  //                           "警告",
  //                           style: TextStyle(
  //                               color: Colors.black,
  //                               fontWeight: FontWeight.bold,
  //                               fontSize: 18),
  //                         ),
  //                       ),
  //                       Padding(
  //                         padding: const EdgeInsets.symmetric(
  //                             vertical: 10, horizontal: 20),
  //                         child: Text(
  //                           "当前系统检测您的密码过于简单，存在泄露风险，需要修改密码",
  //                           style: TextStyle(
  //                               color: Colors.black,
  //                               fontWeight: FontWeight.bold,
  //                               fontSize: 16),
  //                         ),
  //                       ),
  //                       Padding(
  //                         padding: EdgeInsets.only(top: 15, bottom: 20),
  //                         child: GestureDetector(
  //                             onTap: () {
  //                               SpUtil.remove("lastUserId");
  //                               Navigator.of(context).pop();
  //                               G.getCurrentState().pushNamedAndRemoveUntil(
  //                                   RoutePaths.LOGIN,
  //                                   (router) => router == null);
  //                             },
  //                             child: Container(
  //                               padding: EdgeInsets.symmetric(horizontal: 30,vertical: 10),
  //                               decoration: BoxDecoration(
  //                                 color: AppTheme.themeBlue,
  //                                 borderRadius: BorderRadius.circular(10),
  //                               ),
  //                               child: Text("修改",style: TextStyle(color: Colors.white),),
  //                             )),
  //                       )
  //                     ],
  //                   ),
  //                 )));
  //       });
  // }

  //退出
  loginOut() {
    userViewModel.userLogout();
  }

  // 密码是否显示
  void passwordShowOrHide(){
    passwordShow = !passwordShow;
    notifyListeners();
  }

  void showAccountForbiddenAlert(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return Material(
            color: Colors.black.withAlpha(100),
            child: Align(
              alignment: Alignment.center,
              child: Container(
                width: 300,
                height: 200,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10)),
                child: Column(
                  children: [
                    const Spacer(),
                    Text(
                      '该账号为禁用状态',
                      style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontWeight: FontWeight.bold),
                    ),
                    const Spacer(),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      child: Text(
                        '      可联系公证员协助处理，或使用大陆手机号联系4008001820进行咨询处理。',
                        style: TextStyle(
                            fontSize: 15,
                            color: Colors.black,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            exit(1);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(
                                vertical: 10, horizontal: 20),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: AppTheme.themeBlue, width: 1.0),
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              '退出',
                              style: TextStyle(
                                  fontSize: 15, color: AppTheme.themeBlue),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 20,
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                            service.call("4008001820");
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(
                                vertical: 10, horizontal: 20),
                            decoration: BoxDecoration(
                                color: AppTheme.themeBlue,
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              '立即拨打',
                              style:
                                  TextStyle(fontSize: 15, color: Colors.white),
                            ),
                          ),
                        )
                      ],
                    ),
                    const Spacer(),
                  ],
                ),
              ),
            ),
          );
        });
  }

  //密码加密
  String generateMd5(String data) {
    var content = new Utf8Encoder().convert(data);
    var digest = md5.convert(content);
    return digest.toString();
  }

  @override
  Future loadData() {
    return null;
  }

  @override
  onCompleted(data) {}
}
