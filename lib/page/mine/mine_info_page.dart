import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:notarization_station_app/base_framework/widget_state/base_state.dart';
import 'package:notarization_station_app/page/login/vm/user_view_model.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:provider/provider.dart';

import '../../appTheme.dart';
import '../../config.dart';

class MineInfoPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return _MineInfoPageState();
  }
}

class _MineInfoPageState extends BaseState<MineInfoPage> {
  UserViewModel userModel;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: commonAppBar(title: "个人资料"),
      body: Consumer<UserViewModel>(
        builder: (ctx, userModel, child) {
          this.userModel = userModel;
          return buildWidget();
        },
      ),
    );
  }

  Widget buildWidget() {
    return Column(
      children: <Widget>[
        InkWell(
          onTap: () {
            Navigator.pushNamed(context, RoutePaths.EditPortrait);
          },
          child: Container(
            color: Colors.white,
            padding: EdgeInsets.fromLTRB(getWidthPx(30), getHeightPx(20),
                getWidthPx(30), getHeightPx(20)),
            child: Row(
              children: <Widget>[
                Expanded(
                    child: const Text(
                  "头像",
                  style: TextStyle(fontSize: 16, color: AppTheme.dark_grey),
                )),
                ClipRRect(
                    borderRadius: new BorderRadius.circular(getWidthPx(100)),
                    child: userModel.headIcon != null
                        ? FadeInImage.assetNetwork(
                            width: getWidthPx(100),
                            height: getWidthPx(100),
                            placeholder: 'lib/assets/images/on-boy.jpg',
                            image: userModel.hasUser
                                ? Config.splicingImageUrl(userModel.headIcon)
                                : "",
                            fit: BoxFit.fill,
                          )
                        : Image.asset(
                            'lib/assets/images/on-boy.jpg',
                            width: getWidthPx(100),
                            height: getWidthPx(100),
                          ))
              ],
            ),
          ),
        ),
        SizedBox(height: 1),
        Container(
          color: Colors.white,
          padding: EdgeInsets.fromLTRB(
              getWidthPx(30), getHeightPx(20), getWidthPx(30), getHeightPx(20)),
          child: Row(
            children: <Widget>[
              Expanded(
                  child: const Text(
                "姓名",
                style: TextStyle(fontSize: 16, color: AppTheme.dark_grey),
              )),
              Text(
                userModel.userName == null
                    ? "用户${userModel.mobile}"
                    : userModel.userName,
                style: const TextStyle(fontSize: 16, color: AppTheme.dark_grey),
              )
            ],
          ),
        ),
        SizedBox(height: 1),
        Container(
          color: Colors.white,
          padding: EdgeInsets.fromLTRB(
              getWidthPx(30), getHeightPx(20), getWidthPx(30), getHeightPx(20)),
          child: Row(
            children: <Widget>[
              Expanded(
                  child: const Text(
                "手机号",
                style: TextStyle(fontSize: 16, color: AppTheme.dark_grey),
              )),
              Text(
                userModel.mobile ?? "",
                style: const TextStyle(fontSize: 16, color: AppTheme.dark_grey),
              )
            ],
          ),
        ),
      ],
    );
  }
}
