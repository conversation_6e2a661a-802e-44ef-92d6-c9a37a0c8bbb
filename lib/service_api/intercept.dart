/*
 * @Author: 王士博 <EMAIL>
 * @Date: 2023-04-25 19:53:35
 * @LastEditors: <EMAIL> WSBwsb123!@#
 * @LastEditTime: 2024-07-11 08:56:08
 * @FilePath: /sc-remotenotarization-app/lib/service_api/intercept.dart
 * @Description: 
 * 
 * Copyright (c) 2023 by ${git_name_email}, All Rights Reserved. 
 */
import 'dart:io';

import 'package:connectivity/connectivity.dart';
import 'package:dio/dio.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:notarization_station_app/base_framework/utils/toast_util.dart';
import 'package:notarization_station_app/page/login/entity/userInfo.dart';
import 'package:notarization_station_app/routes/router.dart';
import 'package:notarization_station_app/socket/mqtt_client.dart';
import 'package:notarization_station_app/utils/common_tools.dart';
import 'package:notarization_station_app/utils/global.dart';
import 'package:notarization_station_app/utils/network_speed.dart';
import 'package:package_info/package_info.dart';
import 'package:provider/provider.dart';

import '../page/login/vm/user_view_model.dart';

class BaseInterceptor extends Interceptor {
  @override
  onRequest(RequestOptions options) async {
    UserInfoEntity data;
    var entity = SpUtil.getObject("lastUserId");
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    if (entity != null) {
      data = UserInfoEntity.fromJson(entity);
       String token = data.token;
      options.headers["token"] = token;
    }
    options.headers["version"] = packageInfo.version;
    options.headers['packageName'] = packageInfo.packageName;
    options.headers["clientId"] = G.clientId;
    options.headers['secret'] =  encryptSM4Key(G.secret);
    if (Platform.isIOS) {
      options.headers["source"] = "app-sgx-ios";
    } else if (Platform.isAndroid) {
      options.headers["source"] = "app-sgx-android";
    }
    super.onRequest(options);
  }

  @override
  onResponse(Response response) async {
    wjPrint("解密前的数据$response");
    dynamic data = response.data;
    if (data is String) {
      data = wjDecrypt(response.data);
      wjPrint("拦截解析后的数据-----$data");
    }
    if (data is Map) {
      if (data != null && data['code'] == 401) {
        EasyLoading.dismiss();
        SpUtil.remove("lastUserId");
        Provider.of<UserViewModel>(G.getCurrentContext(), listen: false).clearUserInformation();
        wjPrint(
            'ModalRoute.of(G.getGlobalContext()).settings.name--------${G.getCurrentContext()}');
        if (G.currentPath != RoutePaths.LOGIN) {
          G.getCurrentState().pushNamedAndRemoveUntil(
              RoutePaths.LOGIN, (router) => router == null);
        }
        MqttClientMsg.instance.disconnect();
      }
      if (data['code'] != 200) {
        Future.error(response.data);
      }
    }
    response.data = data;
    super.onResponse(response);
  }

  @override
  onError(DioError err) async {
    wjPrint("在错误之前的拦截信息${err.response}");
    EasyLoading.dismiss();
    if (err.type == DioErrorType.CONNECT_TIMEOUT) {
      ToastUtil.showErrorToast("网络较差，请求超时");
    }
    if (err.response != null && err.response.statusCode == 503) {
      ToastUtil.showErrorToast("网络异常，请稍后再试");
    }
    if (err.response != null && err.response.statusCode == 502) {
      ToastUtil.showErrorToast(err.response.data['msg'] ??
          err.response.data["messsage"] ??
          "网络异常，请稍后再试");
    }
    if (err.response != null && err.response.statusCode == 401) {
      EasyLoading.dismiss();
      ToastUtil.showErrorToast("请重新登录");
      SpUtil.remove("lastUserId");
      Provider.of<UserViewModel>(G.getCurrentContext(), listen: false).clearUserInformation();
      wjPrint(
          'ModalRoute.of(G.getGlobalContext()).settings.name--------${G.getCurrentContext()}');
      if (G.currentPath != RoutePaths.LOGIN) {
        G.getCurrentState().pushNamedAndRemoveUntil(
            RoutePaths.LOGIN, (router) => router == null);
      }
      MqttClientMsg.instance.disconnect();
    }
    Future.error(err);
    super.onError(err);
  }
}

class NetWorkTempConnectivityTools {
  //网络进行监听
  initConnectivity() async {
    //平台消息可能会失败，因此我们使用Try/Catch PlatformException。
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        ToastUtil.showWarningToast('网络异常，请稍后再试', second: 3);
        // Future.delayed(const Duration(seconds: 3), () {
        //   return;
        // });
      } else {
        NetworkSpeed().getNetworkSpeed();
      }
    } on PlatformException catch (e) {
      wjPrint("${e.message.toString()}");
    }
  }
}
