import 'package:dio/dio.dart';
import 'package:notarization_station_app/config.dart';

import 'bedrock_http.dart';

class InformationApi {
  static InformationApi _singleton;

  factory InformationApi() => getSingleton();

  static InformationApi getSingleton() {
    if (_singleton == null) {
      _singleton = InformationApi._internal();
    }
    return _singleton;
  }

  InformationApi._internal() {
    //do stuff
  }

  //公证问答
  Future getQuestion(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/question/notarization", data: map,errorCallBack: errorCallBack);
    return response.data;
  }

  //行业动态
  Future getIndustry(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/question/industryNews", data: map,errorCallBack: errorCallBack);
    return response.data;
  }

  Future getQuiz(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/question/add", data: map,
    errorCallBack: errorCallBack);
    return response.data;
  }

  Future getLike(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/likerecord/add", data: map);
    return response.data;
  }

  Future getComment(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/comment/commentPage", data: map);
    return response.data;
  }

  Future addComment(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/comment/add", data: map);
    return response.data;
  }

  Future getReply(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/comment/replyPage", data: map);
    return response.data;
  }

  Future getNotarialOffice(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/notarialOffice/selectPage",
        queryParameters: map);
    return response.data;
  }

  Future getNotaryPublic(map) async {
    final response = await HttpManager.instance.post(
        "${Config.userModule}/cgz/notaryPublic/queryAllByNotaryId",
        options: Options(
          contentType: "application/raw",
        ),
        data: map);
    return response.data;
  }

  Future addFriend(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/approval/add", data: map);
    return response.data;
  }

  Future getFriend(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/friendlist/queryFriend", data: map);
    return response.data;
  }

  Future applyFriend(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/approval/queryApprovalList", data: map);
    return response.data;
  }

  Future approvalFriend(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/approval/approval", data: map);
    return response.data;
  }

  Future queryHistory(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/cgz/talk/queryHistory", data: map);
    return response.data;
  }

  Future getUnread(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/cgz/lastTalk/queryLast", data: map);
    return response.data;
  }

  Future getUnreadUpdate(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/cgz/lastTalk/update", data: map);
    return response.data;
  }

  Future getLikeList(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/likerecord/queryLikeList", data: map,errorCallBack: errorCallBack);
    return response.data;
  }

  Future getOneQuestion(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/question/getOne", data: map);
    return response.data;
  }

  Future getOneArticle(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/article/getOne", data: map);
    return response.data;
  }

  Future getOneComment(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/comment/getOne", data: map);
    return response.data;
  }

  Future getCommentList(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/comment/queryCommentList", data: map,errorCallBack: errorCallBack);
    return response.data;
  }

  Future getNotarial(map,{Function errorCallBack}) async {
    final response = await HttpManager.instance.post(
        "${Config.userModule}/cgz/notarialOffice/addressSortselectPage",
        queryParameters: map,
    errorCallBack: errorCallBack);
    return response.data;
  }

  Future getRecordCount(map) async {
    final response = await HttpManager.instance.get(
        "${Config.parkModule}/sq/comment/recordCount",
        queryParameters: map);
    return response.data;
  }

  Future getCommentUpdate(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/comment/update", data: map);
    return response.data;
  }

  Future getLikerecord(map) async {
    final response = await HttpManager.instance.get(
        "${Config.parkModule}/sq/likerecord/likeRecordCount",
        queryParameters: map);
    return response.data;
  }

  Future getLikeUpdate(map) async {
    final response = await HttpManager.instance
        .post("${Config.parkModule}/sq/likerecord/update", data: map);
    return response.data;
  }

  Future getMaterialModel(map) async {
    final response = await HttpManager.instance.post(
        "${Config.notaryModule}/cgz/materialmodel/selectAll",
        queryParameters: map);
    return response.data;
  }
}
